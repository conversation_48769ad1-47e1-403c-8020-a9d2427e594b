# 赛事申诉功能完善说明

## 完善内容概述

本次完善主要解决了以下问题：
1. 修复了Java方法引用无效的编译错误
2. 完善了申诉校验功能，确保网盘提取码必须是4位
3. 统一了前后端校验逻辑
4. 更新了数据库表结构

## 具体修改内容

### 1. 后端修改

#### 1.1 Appeal实体类 (`perfree-system/perfree-system-biz/src/main/java/com/perfree/model/Appeal.java`)
- 添加了提取码4位长度的校验注解：`@Pattern(regexp = "^.{4}$", message = "提取码必须是4位")`

#### 1.2 AppealController (`perfree-system/perfree-system-biz/src/main/java/com/perfree/controller/competition/appeal/AppealController.java`)
- 集成了AppealValidationService校验服务
- 添加了@Valid注解进行参数校验
- 新增了完整的CRUD接口：
  - `GET /api/competition/appeal/list` - 根据手机号查询申诉列表
  - `GET /api/competition/appeal/all` - 获取所有申诉列表（管理员）
  - `GET /api/competition/appeal/{id}` - 查询申诉详情
  - `POST /api/competition/appeal/review` - 审核申诉

#### 1.3 AppealMapper (`perfree-system/perfree-system-biz/src/main/java/com/perfree/mapper/AppealMapper.java`)
- 修复了方法引用编译错误，使用QueryWrapper替代LambdaQueryWrapper
- 添加了按创建时间倒序排列

#### 1.4 AppealValidationService (`perfree-system/perfree-system-biz/src/main/java/com/perfree/service/competition/appeal/AppealValidationService.java`)
- 已存在完整的校验功能类，包含：
  - 基本字段校验
  - 手机号格式校验
  - 邮箱格式校验
  - 提取码4位长度校验
  - 申诉ID校验
  - 审核状态校验

### 2. 前端修改

#### 2.1 申诉表单 (`perfree-ui-base/src/modules/competition/view/CompetitionAppeal.vue`)
- 完善了提取码校验规则，确保必须是4位：
```javascript
extractionCode: [
  { required: true, message: '请输入提取码', trigger: 'blur' },
  { len: 4, message: '提取码必须是4位', trigger: 'blur' }
]
```

### 3. 数据库修改

#### 3.1 表结构更新 (`sql/appeal_tables.sql`)
- 修改了提取码字段长度限制：`extraction_code` varchar(4)

#### 3.2 数据库更新脚本 (`sql/appeal_update_extraction_code.sql`)
- 提供了更新现有数据库的SQL脚本
- 添加了提取码长度检查约束

### 4. 测试文件

#### 4.1 单元测试 (`perfree-system/perfree-system-biz/src/test/java/com/perfree/service/competition/appeal/AppealValidationServiceTest.java`)
- 创建了完整的校验服务单元测试
- 覆盖了所有校验场景

## 校验规则说明

### 后端校验（使用注解 + 校验服务）
1. **基本字段校验**：所有必填字段不能为空
2. **手机号校验**：使用正则表达式 `^(?:(?:\+|00)86)?1[3-9]\d{9}$`
3. **邮箱校验**：使用正则表达式 `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
4. **提取码校验**：必须是4位字符（数字、字母均可）
5. **申诉ID校验**：必须是正整数
6. **审核状态校验**：只能是 PENDING、APPROVED、REJECTED、PROCESSING

### 前端校验
1. **实时校验**：表单输入时进行格式校验
2. **提交校验**：提交前进行完整性校验
3. **提取码校验**：确保长度为4位

## 使用方式

### 1. 数据库更新
执行以下SQL脚本更新数据库：
```sql
-- 如果是新建表
source sql/appeal_tables.sql

-- 如果是更新现有表
source sql/appeal_update_extraction_code.sql
```

### 2. 后端接口调用
```java
// 提交申诉
POST /api/competition/appeal/submit
Content-Type: application/json
{
  "applicationType": "SCORE_APPEAL",
  "request": "申请重新评分",
  "explanation": "详细说明",
  "evidenceLink": "https://pan.baidu.com/s/test",
  "extractionCode": "1234",  // 必须是4位
  "submitter": "张三",
  "submitterUnit": "测试单位",
  "submitterPhone": "13800138000",
  "submitterEmail": "<EMAIL>"
}
```

## 注意事项

1. **提取码限制**：现在严格限制为4位，任何不符合的提取码都会被拒绝
2. **方法引用问题**：如果仍然遇到编译问题，确保项目使用了正确的Lombok版本
3. **校验顺序**：先进行注解校验，再进行业务校验
4. **错误处理**：所有校验错误都会抛出ServiceException，前端需要正确处理

## 维护建议

1. **校验逻辑集中**：所有校验逻辑都在AppealValidationService中，便于维护
2. **测试覆盖**：已提供完整的单元测试，修改时请同步更新测试
3. **文档更新**：如有新增校验规则，请及时更新此文档
