# 赛事申诉功能完善说明

## 完善内容概述

本次完善主要解决了以下问题：
1. 修复了Java方法引用无效的编译错误
2. 完善了申诉校验功能，确保网盘提取码必须是4位
3. 统一了前后端校验逻辑
4. 创建了专门的校验功能类便于维护

## 具体修改内容

### 1. 后端修改

#### 1.1 新增 AppealValidationService 校验功能类
**文件路径**: `perfree-system/perfree-system-biz/src/main/java/com/perfree/service/competition/appeal/AppealValidationService.java`

**功能特点**:
- 统一管理所有申诉相关的校验逻辑
- 包含基本字段校验、格式校验、业务规则校验
- 便于后续维护和扩展

**主要校验方法**:
- `validateAppealForm()` - 校验申诉表单数据
- `validateExtractionCode()` - 校验提取码必须是4位
- `validatePhoneNumber()` - 校验手机号格式
- `validateEmail()` - 校验邮箱格式
- `validateAppealId()` - 校验申诉ID
- `validateReviewStatus()` - 校验审核状态

#### 1.2 完善 Appeal 实体类
**文件路径**: `perfree-system/perfree-system-biz/src/main/java/com/perfree/model/Appeal.java`

**修改内容**:
- 添加了提取码4位长度的校验注解：`@Pattern(regexp = "^.{4}$", message = "提取码必须是4位")`

#### 1.3 修复 AppealMapper 方法引用问题
**文件路径**: `perfree-system/perfree-system-biz/src/main/java/com/perfree/mapper/AppealMapper.java`

**修改内容**:
- 将 `Appeal::getSubmitterPhone` 方法引用改为使用 `QueryWrapper` 和字段名字符串
- 添加了 `findByStatus()` 方法
- 避免了Lombok编译时可能出现的方法引用问题

#### 1.4 完善 AppealController
**文件路径**: `perfree-system/perfree-system-biz/src/main/java/com/perfree/controller/competition/appeal/AppealController.java`

**修改内容**:
- 集成了 `AppealValidationService` 校验服务
- 添加了 `@Valid` 注解进行参数校验
- 新增了完整的CRUD接口：
  - `GET /api/competition/appeal/list` - 根据手机号查询申诉列表
  - `GET /api/competition/appeal/all` - 获取所有申诉列表（管理员）
  - `GET /api/competition/appeal/{id}` - 查询申诉详情
  - `POST /api/competition/appeal/review` - 审核申诉
  - `GET /api/competition/appeal/status/{status}` - 根据状态查询申诉列表

### 2. 前端修改

#### 2.1 完善申诉表单校验规则
**文件路径**: `perfree-ui-base/src/modules/competition/view/CompetitionAppeal.vue`

**修改内容**:
- 完善了提取码校验规则，确保必须是4位：
```javascript
extractionCode: [
  { required: true, message: '请输入提取码', trigger: 'blur' },
  { len: 4, message: '提取码必须是4位', trigger: 'blur' }
]
```
- 为提取码输入框添加了 `maxlength="4"` 属性，防止用户输入超过4位
- 更新了占位符文本为"请输入4位提取码"

### 3. 数据库修改

#### 3.1 表结构确认
**文件路径**: `sql/appeal_tables.sql`
- 确认提取码字段已设置为 `varchar(4)`

#### 3.2 数据库更新脚本
**文件路径**: `sql/appeal_update_extraction_code.sql`
- 提供了更新现有数据库的SQL脚本
- 添加了提取码长度检查约束

### 4. 测试文件

#### 4.1 单元测试
**文件路径**: `perfree-system/perfree-system-biz/src/test/java/com/perfree/service/competition/appeal/AppealValidationServiceTest.java`
- 创建了完整的校验服务单元测试
- 覆盖了所有校验场景

## 校验规则说明

### 后端校验（使用注解 + 校验服务）
1. **基本字段校验**：所有必填字段不能为空
2. **手机号校验**：使用正则表达式 `^(?:(?:\+|00)86)?1[3-9]\d{9}$`
3. **邮箱校验**：使用正则表达式 `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
4. **提取码校验**：必须是4位字符（数字、字母均可）
5. **申诉ID校验**：必须是正整数
6. **审核状态校验**：只能是 PENDING、APPROVED、REJECTED、PROCESSING

### 前端校验
1. **实时校验**：表单输入时进行格式校验
2. **提交校验**：提交前进行完整性校验
3. **提取码校验**：确保长度为4位，输入框限制最大长度

## 使用方式

### 1. 数据库更新
如果需要更新现有数据库，执行以下SQL脚本：
```sql
source sql/appeal_update_extraction_code.sql
```

### 2. 后端接口调用示例
```bash
# 提交申诉
POST /api/competition/appeal/submit
Content-Type: application/json

{
  "applicationType": "SCORE_APPEAL",
  "request": "申请重新评分",
  "explanation": "详细说明申诉原因",
  "evidenceLink": "https://pan.baidu.com/s/test",
  "extractionCode": "1234",  // 必须是4位
  "submitter": "张三",
  "submitterUnit": "测试单位",
  "submitterPhone": "13800138000",
  "submitterEmail": "<EMAIL>"
}
```

### 3. 运行测试
```bash
# 运行校验服务测试
mvn test -Dtest=AppealValidationServiceTest
```

## 注意事项

1. **提取码限制**：现在严格限制为4位，任何不符合的提取码都会被拒绝
2. **方法引用问题**：已使用QueryWrapper替代Lambda方法引用，避免编译问题
3. **校验顺序**：先进行注解校验，再进行业务校验
4. **错误处理**：所有校验错误都会抛出ServiceException，前端需要正确处理

## 维护建议

1. **校验逻辑集中**：所有校验逻辑都在AppealValidationService中，便于维护
2. **测试覆盖**：已提供完整的单元测试，修改时请同步更新测试
3. **文档更新**：如有新增校验规则，请及时更新此文档
