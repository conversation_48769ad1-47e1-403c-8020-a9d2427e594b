### 分片上传基本入参:

|字段名|约束| 备注                                 |
| ------ | ---- | ------------------------------ |
| name | String | 上传文件的文件名称 |
| chunks | int | 总分片数量 |
| chunk | int | 当前为第几块分片 |
| chunkSize | Long | 按多大的文件粒度进行分片 |
| file | MultipartFile | 分片文件对象 |
| MD5 | String(32) | 文件MD5，校验MD5实现妙传 |
| size | Long | 当前分片大小 |
| uid | UUID | 文件唯一标识UUID(无横线32位UUID) |
|        |      |      |

### 分片上传常用方法:

·    核心上传方法

```java
public static SplitFileRespVO uploadSplitFile(BaseSplitFileVO reqVO) throws IOException
```

·    创建临时文件

```java
public static boolean checkAndSetUploadProgress(BaseSplitFileVO reqVO)
```

·    所有分片上传完毕，保存文件

```java
public static boolean finishUpload(File tempFile, Path savePath) throws IOException
```

·    获取文件MD5，用于校验文件是否已存在，实现妙传

```java
public static String getFileMD5(MultipartFile file) throws IOException, NoSuchAlgorithmException
```

```java
public static String getFileMD5(File file) throws IOException, NoSuchAlgorithmException
```
