

### 赛事主表（competition）

| 字段名             |  类型          |  必填  | 说明           | 前端字段名        | 备注                                         |
| ------------------ | ------------ | :--: | -------- | ----------------- | -------------------------------------------- |
| id                 | UUID  | 否   | 主键     | -                 | 赛事ID，后端生成                            |
| code               | VARCHAR(32)  | 是   | 赛事编号 | code              | 赛事编号，前端生成，后端校验唯一               |
| name               | VARCHAR(128) | 是   | 赛事名称 | name              |                                              |
| type               | VARCHAR(32)  | 是   | 赛事类型 | type              | academic/sports/innovation/art               |
| level              | VARCHAR(32)  | 是   | 赛事级别 | level             | national/provincial/municipal/school/college |
| participation_type | VARCHAR(32)  | 是   | 参赛形式 | participationType | individual/team                              |
| organizer          | VARCHAR(128) | 是   | 主办方   | organizer         |                                              |
| enroll_start_time | DATETIME     | 否   | 报名开始     | enrollStartTime |                             |
| enroll_end_time   | DATETIME     | 否   | 报名结束     | enrollEndTime   |                             |
| description        | TEXT         | 是   | 简介     | description       |                                              |
| rules              | TEXT         | 是   | 规则     | rules             |                                              |
| official_files     | 数据库字表 | 否   | 官方文件 | officialFiles     | 见下                                         |
| promotional_video  | 数据库子表  | 否   | 宣传视频 | promotionalVideo  | 见下                                         |

注意：宣传视频（promotionalVideo）字段

### 官方文件（officialFiles）/宣传视频（promotionalVideo）

#### 官方文件表

| 字段名         | 类型         | 说明         |
| -------------- | ------------ | ------------ |
| id             | UUID         | 主键         |
| competition_id | UUID         | 赛事ID，外键 |
| file_name      | VARCHAR(128) | 文件名       |
| file_url       | VARCHAR(255) | 文件地址     |
| uid            | UUID         | 唯一标识     |

·    推荐都用数组，结构一致：

```json
[
  {
    "name": "xxx.pdf",
    "url": "https://yourdomain.com/upload/xxx.pdf",
    "uid": "1681234567890"
  }
]
```

·    数据库存储建议：可单独表（推荐，便于扩展），或主表JSON字段。



### 阶段子表（competition_stage）

| 字段名            | 类型         | 必填 | 说明         | 前端字段名      | 备注                                 |
| ----------------- | ------------ | ---- | ------------ | --------------- | ------------------------------------ |
| id                | UUID         | 否   | 主键         | -               | 后端生成                             |
| competition_id    | UUID         | 是   | 赛事ID，外键 | -               |                                      |
| stage_type        | VARCHAR(32)  | 是   | 阶段类型     | stageType       | preliminary/semifinal/final          |
| stage_form        | VARCHAR(32)  | 是   | 举办形式     | stageForm       | online/offline                       |
| judge_start_time  | DATETIME     | 否   | 评审开始     | judgeStartTime  |                                      |
| judge_end_time    | DATETIME     | 否   | 评审结束     | judgeEndTime    | 第一阶段评审结束时间作为赛事结束时间 |
| submit_start_time | DATETIME     | 否   | 提交开始     | submitStartTime | 第一阶段提交开始时间作为赛事开始时间 |
| submit_end_time   | DATETIME     | 否   | 提交结束     | submitEndTime   |                                      |
| location          | VARCHAR(128) | 否   | 地点         | location        |                                      |
| leader            | VARCHAR(32)  | 是   | 负责人姓名   | leader          |                                      |
| leader_phone      | VARCHAR(32)  | 是   | 负责人电话   | leaderPhone     |                                      |
| remark            | VARCHAR(255) | 否   | 阶段说明     | remark          |                                      |
| order_num         | INT          | 否   | 阶段顺序     | -               | 阶段顺序                             |



### 新增赛事接口参数（Body JSON）

·    接口地址：POST /api/competition/add

·    功能：新增赛事

```json
{
  "code": "COMP20240510153045ABC123",
  "name": "2025年全国大学生数学竞赛",
  "type": "academic",
  "level": "national",
  "participation_type": "individual",
  "organizer": "中国数学会",
  "enroll_start_time": "2026-04-20T09:00:00",
  "enroll_end_time": "2026-04-30T18:00:00",
  "description": "赛事简介",
  "rules": "赛事规则",
  "stages": [
    {
      "stage_type": "preliminary",
      "stage_form": "online",
      "judge_start_time": "2026-05-01T09:00:00",
      "judge_end_time": "2026-05-05T18:00:00",
      "submit_start_time": "2026-05-01T09:00:00",
      "submit_end_time": "2026-05-05T18:00:00",
      "location": "线上",
      "leader": "李白",
      "leader_phone": "13812345678",
      "remark": "初赛说明"
    },
    {
      "stage_type": "preliminary",
      "stage_form": "online",
      "judge_start_time": "2026-05-01T09:00:00",
      "judge_end_time": "2026-05-05T18:00:00",
      "submit_start_time": "2026-05-01T09:00:00",
      "submit_end_time": "2026-05-05T18:00:00",
      "location": "线上",
      "leader": "张三",
      "leader_phone": "13812345678",
      "remark": "初赛说明"
    }
  ]
}
```



### 新增赛事接口返回参数（Body JSON）

```json
{
  "code": 200,
  "data": {
    "code": "",
    "name": "",
    "type": "",
    "level": "",
    "participation_type": "",
    "organizer": "",
    "description": "",
    "rules": "",
  },
  "msg": ""
}
```



### 删除赛事接口参数（Body JSON）

·    接口地址：POST /api/competition/delect

·    功能：删除赛事

```json
{
  "code": "COMP20240510153045ABC123"
}
```



### 删除赛事接口返回参数（Body JSON）

```json
{
  "code": 200,
  "data": {
    "code": "COMP20240510153045ABC123",
    "delectCompetitionNum": 1,
    "delectTime": "2025-07-13T17:45:52"
  },
  "msg": ""
}
```



### 获取赛事接口参数

### 接口说明

·    接口地址：GET /api/competition/get?code=xxx

·    功能：获取赛事

### 返回参数（Body JSON）

```json
{
  "code": 200,
  "data": {
    "code": "COMP20240510153045ABC123",
    "name": "123",
    "type": "academic",
    "level": "national",
    "participation_type": "individual",
    "organizer": "123",
    "description": "123",
    "rules": "123",
    "stages": [
      {
        "stage_type": "preliminary",
        "stage_form": "online",
        "judge_start_time": null,
        "judge_end_time": null,
        "enroll_start_time": null,
        "enroll_end_time": null,
        "submit_start_time": null,
        "submit_end_time": null,
        "location": "线上",
        "leader": "张三",
        "leader_phone": "13812345678",
        "remark": "初赛说明",
        "order_num": 2
      },
      {
        "stage_type": "preliminary",
        "stage_form": "online",
        "judge_start_time": "2026-05-01T09:00:00",
        "judge_end_time": "2026-05-05T18:00:00",
        "enroll_start_time": "2026-04-20T09:00:00",
        "enroll_end_time": "2026-04-30T18:00:00",
        "submit_start_time": "2026-05-01T09:00:00",
        "submit_end_time": "2026-05-05T18:00:00",
        "location": "线上",
        "leader": "李白",
        "leader_phone": "13812345678",
        "remark": "初赛说明",
        "order_num": 1
      }
    ],
    "official_files": [
      {
        "file_name": "2234",
        "file_url": "1354"
      },
      {
        "file_name": "111",
        "file_url": "123"
      }
    ],
    "promotional_video_files": [
      {
        "file_name": "123",
        "file_url": "12"
      }
    ]
  },
  "msg": ""
}
```



## 赛事编号唯一性校验

### 接口说明

·    接口地址：GET /api/competition/checkCodeUnique?code=xxx

·    功能：校验赛事编号是否唯一

### 返回参数（Body JSON）

·    唯一

```json
{
  "code": 200,
  "data": {
    "code": "COMP20240510153045ABC123",
    "unique": true
  },
  "msg": ""
}
```

·    已存在

```json
{
  "code": 200,
  "data": {
    "code": "COMP20240510153045ABC123",
    "unique": false
  },
  "msg": ""
}
```

 

### 上传赛事文件（multipart/form-data）

·    接口地址：POST /api/competition/uploadOfficialFile

·    功能：上传赛事文件

·    请求参数：

o  competition_code（String，必填）：赛事编号

o  name（String，必填）：文件名

o  chunks（int，必填）：总分片数量

o  chunk（int，必填）：当前分片

o  chunkSize（Long，必填）：按多大的文件粒度进行分片

o  file（File，必填）：图片文件（jpg/png，最大10MB）

o  md5（String(32)，必填）：文件MD5

o  size（Long，必填）：当前分片大小

o  allSize（Long，必填）：整个文件大小

o  uid（String(32)，必填）：文件唯一标识UUID

·    返回示例：

```json
{
    "code": 500,
    "data": {
        "chunk": 0
        finishUpload: ;
    },
    "msg": ""
}
```

·    校验规则：

o  仅允许 doc/docx/pdf/xls/xlsx 格式，最大10MB



### 报名赛事接口（Body JSON）

·    接口地址：POST /api/competition/enrollCompetition

·    功能：报名赛事

·    请求参数：

```json
{
  "competitionCode": "",//赛事编号
  "teamId": "",//团队ID
  "applicantId": 0 //用户ID
}
```

·    返回示例：

```json
{
  "competitionCode": "",//赛事编号
  "teamId": "",//团队ID
  "applicantId": 0 //用户ID
}
```



### 获取所有赛事接口

·    接口地址：GET /api/competition/getall

·    功能：获取所有赛事

·    返回示例：

```json
{
  "code": 200,
  "data": [
    {
      "id": "157e25346776df0d4e51fc09664cfbd3",
      "code": "COMP202507212132564AJCTM",
      "name": "nba",
      "type": "academic",
      "level": "national",
      "participation_type": "individual",
      "organizer": "科技局",
      "enroll_start_time": "2025-07-22T00:00:00",
      "enroll_end_time": "2025-07-24T00:00:00",
      "description": "规则",
      "rules": "评分",
      "status": 0,
      "current_stage": 0
    },
    {
      "id": "38c1b0f4fb64e1fa62a8ef01381b0d01",
      "code": "COMP20250721213702HJDWDR",
      "name": "nba",
      "type": "academic",
      "level": "national",
      "participation_type": "individual",
      "organizer": "科技局",
      "enroll_start_time": "2025-07-22T00:00:00",
      "enroll_end_time": "2025-07-24T00:00:00",
      "description": "规则",
      "rules": "评分",
      "status": 0,
      "current_stage": 0
    },
   
    {
      "id": "876a115b0d48789c8d959aa3cfcceb50",
      "code": "COMP202507212137154WO7P8",
      "name": "nba",
      "type": "academic",
      "level": "national",
      "participation_type": "individual",
      "organizer": "科技局",
      "enroll_start_time": "2025-07-22T00:00:00",
      "enroll_end_time": "2025-07-23T00:00:00",
      "description": "规则",
      "rules": "评分",
      "status": 0,
      "current_stage": 0
    }
  ],
  "msg": ""
}
```



### 获取赛事当前阶段接口

·    接口地址：GET /api/competition/getCurrentStage

·    功能：获取赛事当前阶段

·    请求参数：

```json
{
  "competitionId": ""
}
```

·    返回示例：

```json
{
  "code": 200,
  "data": {
    "id": "c917d6c7599b7e20bafcf0518c608395",
    "competition_code": "COMP20240510153045ABC123",
    "stage_type": "preliminary",
    "stage_form": "online",
    "judge_start_time": "2026-05-01T09:00:00",
    "judge_end_time": "2026-05-05T18:00:00",
    "submit_start_time": "2026-05-01T09:00:00",
    "submit_end_time": "2026-05-05T18:00:00",
    "location": "线上",
    "leader": "李白",
    "leader_phone": "13812345678",
    "remark": "初赛说明",
    "order_num": 1,
    "status": 0
  },
  "msg": ""
}
```



### 定时任务

自动转换**赛事状态**和**阶段状态**

