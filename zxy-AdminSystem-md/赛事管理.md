# 赛事管理 API 接口文档

## 1. 批量删除赛事

### 接口信息
- **接口地址**: `POST /api/competition/batchDelete`
- **功能**: 批量删除指定的赛事
- **请求方式**: POST
- **Content-Type**: application/json

### 请求参数

#### Body JSON
```json
{
  "codes": ["COMP001", "COMP002", "COMP003"]
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| codes | Array<String> | 是 | 要删除的赛事编号数组 |

### 返回示例

#### 成功响应
```json
{
  "code": 200,
  "data": {
    "deletedCount": 3,
    "failedCodes": []
  },
  "msg": "批量删除成功"
}
```

#### 失败响应
```json
{
  "code": 400,
  "data": null,
  "msg": "删除失败：赛事不存在"
}
```

#### 部分成功响应
```json
{
  "code": 200,
  "data": {
    "deletedCount": 2,
    "failedCodes": ["COMP003"]
  },
  "msg": "部分删除成功，COMP003 删除失败"
}
```

### 返回参数说明
| 参数名 | 类型 | 说明 |
|--------|------|------|
| deletedCount | Integer | 成功删除的赛事数量 |
| failedCodes | Array<String> | 删除失败的赛事编号数组 |

---

## 2. 导出赛事数据

### 接口信息
- **接口地址**: `POST /api/competition/export`
- **功能**: 导出赛事数据为Excel或CSV格式
- **请求方式**: POST
- **Content-Type**: application/json
- **响应类型**: application/octet-stream (文件下载)

### 请求参数

#### Body JSON
```json
{
  "exportType": "excel",
  "includeAll": false,
  "selectedFields": ["name", "code", "type", "status", "level"],
  "fileName": "赛事数据_2024-01-15",
  "name": "",
  "code": "",
  "type": "",
  "organizer": "",
  "level": "",
  "participation_type": "",
  "pageNo": 1,
  "pageSize": 10,
  "enroll_start_time": "",
  "enroll_end_time": ""
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| exportType | String | 是 | 导出格式：excel 或 csv |
| includeAll | Boolean | 是 | 是否导出全部数据（true）或仅当前筛选结果（false） |
| selectedFields | Array<String> | 是 | 要导出的字段列表 |
| fileName | String | 否 | 文件名前缀，不包含扩展名 |
| name | String | 否 | 赛事名称筛选条件 |
| code | String | 否 | 赛事编号筛选条件 |
| type | String | 否 | 赛事类型筛选条件 |
| organizer | String | 否 | 主办方筛选条件 |
| level | String | 否 | 赛事级别筛选条件 |
| participation_type | String | 否 | 参赛形式筛选条件 |
| pageNo | Integer | 否 | 当前页码（当includeAll=false时使用） |
| pageSize | Integer | 否 | 每页大小（当includeAll=false时使用） |
| enroll_start_time | String | 否 | 报名开始时间筛选条件 |
| enroll_end_time | String | 否 | 报名结束时间筛选条件 |

#### 可选字段列表
```javascript
const availableFields = [
  { key: 'name', label: '赛事名称' },
  { key: 'code', label: '赛事编号' },
  { key: 'type', label: '赛事类型' },
  { key: 'status', label: '状态' },
  { key: 'level', label: '级别' },
  { key: 'organizer', label: '主办方' },
  { key: 'enroll_start_time', label: '报名开始时间' },
  { key: 'enroll_end_time', label: '报名结束时间' },
  { key: 'description', label: '赛事简介' },
  { key: 'participation_type', label: '参赛形式' }
]
```

### 返回示例

#### 成功响应
- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet` (Excel)
- **Content-Type**: `text/csv;charset=utf-8` (CSV)
- **文件名**: `赛事数据_2024-01-15.xlsx` 或 `赛事数据_2024-01-15.csv`

#### 失败响应 (JSON格式)
```json
{
  "code": 400,
  "data": null,
  "msg": "导出失败：无数据可导出"
}
```

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

---

## 3. 批量更新赛事状态

### 接口信息
- **接口地址**: `POST /api/competition/batchUpdateStatus`
- **功能**: 批量更新赛事状态
- **请求方式**: POST
- **Content-Type**: application/json

### 请求参数

#### Body JSON
```json
{
  "competitions": [
    {
      "code": "COMP001",
      "status": 1
    },
    {
      "code": "COMP002", 
      "status": 2
    }
  ]
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| competitions | Array<Object> | 是 | 要更新的赛事数组 |
| competitions[].code | String | 是 | 赛事编号 |
| competitions[].status | Integer | 是 | 状态值：0-待开始，1-进行中，2-已结束 |

### 返回示例

#### 成功响应
```json
{
  "code": 200,
  "data": {
    "updatedCount": 2,
    "failedCodes": []
  },
  "msg": "批量更新状态成功"
}
```

#### 失败响应
```json
{
  "code": 400,
  "data": null,
  "msg": "更新失败：赛事不存在"
}
```
