## 1.数据库设计

### 1.1 团队数据表(c_team)

|  字段名  |  类型  |  必填  |  说明  |  前端字段名  |  备注  |
| :--------- | :------- | :--: | :-------- | :----------------- | :-------------------------------------------- |
| id | varchar(32) | 否 | 主键 |  | UUID(32位无横线) |
| name | varchar(64) | 是 | 团队名 | name | 唯一，不超过64字符 |
| description | text | 否 | 团队描述 | description | 非必填 |
| createTime | datetime | 否 | 创建时间 |  |  |
| updateTime | datetime | 否 | 更新时间 |  |  |



### 1.2 团队成员表(c_team_members)

|  字段名  |  类型  |  必填  |  说明  |  前端字段名  |  备注  |
| :--------- | :------- | :--: | :---- | :----------------- | :-------------------------------------------- |
| id | UUID | 否 | 主键 |      | 主键/UUID(32位无横线) |
| team_id | UUID | 是 | 团队ID | teamId | 外键/UUID(32位无横线) |
| user_id | int | 是 | 用户ID | userId | 外键 |
| role | tinyint | 是 | 成员角色 | role | 成员角色(0:队长, 1:成员, 2:指导老师) |
| createTime | datetime | 否 | 创建时间 | | 创建时自动更新 |
| updateTime | datetime | 否 | 更新时间 | | 自动更新 |
|      |      |      |      |      |      |

### 1.3 团队报名赛事表(c_registrations)

|  字段名  |  类型  |  必填  |  说明  |  前端字段名  |  备注  |
| :--------- | :------- | :--: | :---- | :----------------- | :-------------------------------------------- |
| id | varchar | 否 | 报名表ID |      | 主键/UUID(32位无横线) |
| competition_code | varchar | 是 | 赛事ID | competitionId | 外键/UUID(32位无横线) |
| team_id | varchar | 是 | 团队ID | teamId | 外键/UUID(32位无横线) |
| status | tinyint | 是 | 申请状态 | status | 报名状态（0:待审核、1:已通过、2:被拒绝) |
| createTime | datetime | 否 | 申请时间 |  | 创建时自动更新 |
| updateTime | datetime | 否 | 审核时间 | | 自动更新 |
|      |      |      |      |      |      |

## 2.接口设计

### 2.1 创建团队

·  接口地址:  POST /api/competition/team/create_team
·  请求体（application/json）:

```json
{
  "userId": ,//int
  "name": "",//string
  "description": ""//string
}
```

·  返回示例:

```json
{
  "code": 200,
  "data": {
    "name": "",
    "description": ""
  },
  "msg": ""
}
```



### 2.2 邀请加入团队

·  接口地址:  POST /api/competition/team/invite
·  请求体（application/json）:

```json
{
  "teamId": "e1258970e3945ee42cfb9e0984e2f926",
  "inviterId": 12,
  "inviteeId": 13
}
```

·  返回示例:

```json
{
  "code": 200,
  "data": "已发送确认邮箱, 等待对方确认!",
  "msg": ""
}
```



### 2.3 接受邀请邮件

·  接口地址:  GET /api/competition/team//confirm_invite/{uuid}

·    请求参数：

o  uuid（string，必填）：临时邀请链接UUID

·  邮件示例：

<img src="C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250721015644502.png" alt="image-20250721015644502" style="zoom:25%;float:left;" />
