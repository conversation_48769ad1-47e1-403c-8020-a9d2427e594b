## 1. 数据库表设计建议

### 表名：teacher_info

| 字段名      | 类型         | 说明          | 约束/备注    |
| ----------- | ------------ | ------------- | ------------ |
| id          | UUID         | 主键      | 后端生成     |
| user_id     | int          | 外键          | 关联user     |
| name        | varchar(32)  | 姓名          | 非空         |
| work_id     | varchar(32)  | 工号          | 非空，唯一   |
| phone       | varchar(16)  | 手机号        | 非空         |
| email       | varchar(64)  | 邮箱          | 非空         |
| education   | varchar(16)  | 学历          | 非空         |
| entry_year  | datetime     | 入职年份      | 非空         |
| position    | varchar(32)  | 职称          | 非空         |
| department  | varchar(64)  | 任职学校      | 非空         |
| work_photo  | varchar(255) | 工作证照片URL | 非空         |
| create_time | datetime     | 创建时间      | 默认当前时间 |
| update_time | datetime     | 更新时间      |              |



## 2. 接口设计

### 2.1 获取教师信息

·    接口地址：GET /api/competition/teacher/get

·    请求参数：

o  userId（int，必填）：用户ID

·    返回示例：

```json
{
  "code": 200,
  "data": {
    "name": "",
    "work_id": "",
    "phone": "",
    "email": "",
    "education": "",
    "entry_year": "",
    "position": "",
    "department": "",
    "work_photo": ""
  },
  "msg": ""
}
```



### 2.2 更新教师信息

·    接口地址：POST /api/competition/teacher/update

·    请求体（application/json）：

| 字段       | 类型   | 说**明** | 校验规则/备注              |
| ---------- | ------ | -------- | :------------------------- |
| userId     | int    | 用户ID   | 非空                       |
| name       | string | 姓名     | 非空，2-20字符             |
| work_id    | string | 工号     | 非空，唯一                 |
| phone      | string | 手机号   | 非空，11位手机号           |
| email      | string | 邮箱     | 非空，邮箱格式             |
| education  | string | 学历     | 非空，本科/硕士/博士等     |
| entry_year | string | 入职年份 | 非空，4位数字，1980-当前年 |
| position   | string | 职称     | 非空                       |
| department | string | 任职学校 | 非空                       |

·    返回示例：

```json
{
  "code": 200,
  "data": {
    "success": true,
    "updateTime": "2025-07-16T16:18:13"
  },
  "msg": ""
}
```



### 2.3 上传工作证照片

·    接口地址：POST /api/competition/teacher/upload_work_photo

·    请求类型：multipart/form-data

·    请求参数：

o  userId（int，必填）：用户ID

o  name（String，必填）：文件名

o  chunks（int，必填）：总分片数量

o  chunk（int，必填）：当前分片

o  chunkSize（Long，必填）：按多大的文件粒度进行分片

o  file（File，必填）：图片文件（jpg/png，最大2MB）

o  md5（String(32)，必填）：文件MD5

o  size（Long，必填）：当前分片大小

o  allSize（Long，必填）：整个文件大小

o  uid（String(32)，必填）：文件唯一标识UUID

·    返回示例：

```json
{
    "code": 500,
    "data": {
        "chunk": 0
        finishUpload: ;
    },
    "msg": ""
}
```

·    校验规则：

o  仅允许 jpg/png 格式，最大2MB

o  图片存储路径建议为 /resources/photo/{userID}.jpg



### 2.4 获取工作证照片

·    接口地址：POST /api/competition/teacher/getTWP/{userId}

o  userId（int，必填）：用户ID，动态拼接地址



### 2.5 获取学校列表

·    接口地址：GET /api/common/schools

·    返回示例：

```json
{
  "code": 200,
  "data": {
    "30": {
      "school_id": "30",
      "name": "北京工业大学",
      "level": "本科",
      "school_type": "本科",
      "local": "北京市",
      "site": "https://admissions.bjut.edu.cn/"
    },
    "31": {
      "school_id": "31",
      "name": "北京大学",
      "level": "本科",
      "school_type": "本科",
      "local": "北京市",
      "site": "https://www.gotopku.cn/"
    },
    ...
    "3822": {
      "school_id": "3822",
      "name": "山西医科大学继续教育学院",
      "level": "",
      "school_type": "",
      "local": "山西太原市",
      "site": ""
    }
  },
  "msg": "学校列表"
}
```



## 3. 校验与业务逻辑

·    手机号、邮箱格式：严格校验格式。

·    入职年份：4位数字

·    图片上传：必须先填写工号，图片URL需回填表单。（不想调了）



## 4. 前后端交互流程

1. 前端页面加载，调用 /api/common/schools 获取学校下拉。
2. 用户填写工号后，点击“保存”时，前端优先用表单内工号，无则用 sessionStorage。
3. 上传图片时，前端校验工号必填，上传成功后将图片URL写入表单。
4. 提交表单时，所有字段一次性提交，后端校验并保存。
5. 查询教师信息时，前端传userID，后端返回完整信息。



