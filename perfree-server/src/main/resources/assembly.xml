<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
    <id>${version}</id>
    <formats>
        <format>zip</format>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${basedir}/src/main/bin</directory>
            <outputDirectory/>
            <fileMode>0755</fileMode>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>${basedir}/src/main/resources</directory>
            <includes>
                <include>*.yaml</include>
            </includes>
            <outputDirectory>config</outputDirectory>
            <fileMode>0644</fileMode>
        </fileSet>
        <fileSet>
            <directory>${basedir}/src/main/resources</directory>
            <includes>
                <include>static/**</include>
            </includes>
            <outputDirectory>resources</outputDirectory>
            <fileMode>0644</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <includes>
                <include>${project.artifactId}.jar</include>
            </includes>
            <outputDirectory/>
        </fileSet>
    </fileSets>
</assembly>