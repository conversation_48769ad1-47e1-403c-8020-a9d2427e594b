import { l as a, h as e, a as s, b as t, f as i, d as l, c as o, E as r, i as h, s as c, e as n, g as p, j as g, k as u, r as m, m as y, n as d, o as f, p as S, q as K, t as b, u as k, v, w, x, y as C } from "./@codemirror.js";
const A = [
  a(),
  e(),
  s(),
  t(),
  i(),
  l(),
  o(),
  r.allowMultipleSelections.of(!0),
  h(),
  c(n, { fallback: !0 }),
  p(),
  g(),
  u(),
  m(),
  y(),
  d(),
  f(),
  S.of([
    ...K,
    ...b,
    ...k,
    ...v,
    ...w,
    ...x,
    ...C
  ])
];
export {
  A as b
};
