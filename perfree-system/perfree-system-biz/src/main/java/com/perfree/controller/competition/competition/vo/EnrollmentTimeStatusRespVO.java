package com.perfree.controller.competition.competition.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Schema(description = "报名时间状态响应VO")
@Data
public class EnrollmentTimeStatusRespVO {

    @Schema(description = "赛事编号", example = "COMP20250619001")
    private String competitionCode;

    @Schema(description = "报名开始时间", example = "2025-06-01T00:00:00")
    private LocalDateTime enrollStartTime;

    @Schema(description = "报名结束时间", example = "2025-08-19T23:59:59")
    private LocalDateTime enrollEndTime;

    @Schema(description = "当前时间", example = "2025-07-29T15:30:00")
    private LocalDateTime currentTime;

    @Schema(description = "报名状态", example = "ONGOING")
    private String status;

    @Schema(description = "状态描述", example = "报名进行中")
    private String statusDescription;

    @Schema(description = "是否可以报名", example = "true")
    private Boolean canEnroll;

    @Schema(description = "距离报名开始的秒数", example = "3600")
    private Long secondsToStart;

    @Schema(description = "距离报名结束的秒数", example = "86400")
    private Long secondsToEnd;

    public void calculateTimeDifferences() {
        if (currentTime != null) {
            if (enrollStartTime != null) {
                secondsToStart = java.time.Duration.between(currentTime, enrollStartTime).getSeconds();
                if (secondsToStart < 0) secondsToStart = 0L;
            }
            if (enrollEndTime != null) {
                secondsToEnd = java.time.Duration.between(currentTime, enrollEndTime).getSeconds();
                if (secondsToEnd < 0) secondsToEnd = 0L;
            }
        }
    }
}
