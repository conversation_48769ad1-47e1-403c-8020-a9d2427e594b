package com.perfree.controller.competition.competition;

import com.alibaba.fastjson2.JSONObject;
import com.perfree.commons.common.CommonResult;
import com.perfree.commons.common.PageResult;
import com.perfree.controller.competition.competition.enums.CompetitionStatus;
import com.perfree.controller.competition.competition.vo.*;
import com.perfree.controller.competition.competition.vo.EnrollmentTimeStatusRespVO;
import com.perfree.controller.competition.competition.vo.RegistrationStatusRespVO;
import com.perfree.controller.competition.competition.vo.SimpleRegistrationReqVO;
import com.perfree.controller.competition.competition.vo.SimpleRegistrationRespVO;
import com.perfree.controller.competition.competition.vo.UserRegisteredCompetitionRespVO;
import com.perfree.controller.competition.team.TeamRole;
import com.perfree.convert.competition.competition.CompetitionConvert;
import com.perfree.convert.competition.registration.SimpleRegistrationConvert;
import com.perfree.convert.competition.team.CRegistrationConvert;
import com.perfree.enums.ErrorCode;
import com.perfree.model.CRegistrations;
import com.perfree.model.Competition;
import com.perfree.model.CompetitionOfficialFile;
import com.perfree.model.CompetitionStage;
import com.perfree.security.SecurityFrameworkUtils;
import com.perfree.security.vo.LoginUserVO;
import com.perfree.service.competition.competition.CompetitionService;
import com.perfree.service.competition.team.CRegistrationsService;
import com.perfree.service.competition.team.CTeamMembersService;
import com.perfree.service.competition.team.CTeamService;
import com.perfree.util.FileUtil;
import com.perfree.util.LambdaUtils;
import com.perfree.util.filevo.SplitFileRespVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

@RestController 
@Tag(name = "赛事管理相关接口")
@RequestMapping("/api/competition")
public class CompetitionController {
    private final Timer timer = new Timer();
    private final Map<String, BaseTask> taskMap = new ConcurrentHashMap<>();

    @Value("${competition-resources.competition-file-base-path}")
    private String CFBasePath;

    @Resource
    private CompetitionService competitionService;

    @Resource
    private CTeamService teamService;

    @Resource
    private CTeamMembersService teamMembersService;

    @Resource
    private CRegistrationsService registrationsService;

    @Transactional
    @PostMapping("/add")
    public CommonResult<AddCompetitionRespVO> addCompetition(@RequestBody @Valid AddCompetitionReqVO reqVO) {
        if (competitionService.findByCompetitionCode(reqVO.getCode()) != null)
            return CommonResult.error(ErrorCode.DUPLICATE_CODE);
        if (reqVO.getEnroll_start_time().isAfter(reqVO.getEnroll_end_time())) {
            return CommonResult.error(500, "开始时间不能晚于结束时间");
        }

        Competition competition = competitionService.addCompetition(CompetitionConvert.INSTANCE.convert(reqVO));
        String competitionCode = competition.getCode();

        AtomicReference<CompetitionStage> currentStage = new AtomicReference<>();
        AtomicReference<Boolean> error = new AtomicReference<>(false);
        AtomicReference<CommonResult<AddCompetitionRespVO>> result = new AtomicReference<>();
        CompetitionConvert.INSTANCE.convertListOfStages(reqVO.getStages()).forEach(LambdaUtils.consumerWithIndex((stage, index) -> {
            if (error.get()) return;
            if (stage.getSubmit_start_time().isBefore(reqVO.getEnroll_end_time())) {
                result.set(CommonResult.error(500, "提交开始时间不能早于赛事报名结束时间"));
                error.set(true);
                return;
            }
            if (stage.getSubmit_start_time().isAfter(stage.getSubmit_end_time())
                    || stage.getJudge_start_time().isAfter(stage.getJudge_end_time())) {
                result.set(CommonResult.error(500, "开始时间不能晚于结束时间"));
                error.set(true);
                return;
            }
            if (index > 0 && null != currentStage.get()) {
                if (stage.getJudge_start_time().isAfter(currentStage.get().getJudge_end_time())) {
                    result.set(CommonResult.error(500, "评审开始时间不能晚于上一阶段的评审结束时间"));
                    error.set(true);
                    return;
                }
                if (stage.getJudge_start_time().isBefore(stage.getSubmit_end_time())) {
                    result.set(CommonResult.error(500, "评审开始时间不能早于提交结束时间"));
                    error.set(true);
                    return;
                }
            }

            currentStage.set(stage);

            stage.setCompetition_code(competitionCode);
            stage.setOrder_num(index + 1);
            competitionService.addStage(stage);
        }));
        if (error.get()) return result.get();

        competitionService.selectListOfStageByCompetitionID(reqVO.getCode()).forEach(LambdaUtils.consumerWithIndex((stage, index) -> addStageTask(reqVO.getCode(), stage)));
        addComTask(reqVO.getCode(),
                reqVO.getStages().get(0).getSubmit_start_time(),
                reqVO.getStages().get(reqVO.getStages().size() - 1).getJudge_end_time());

//        List<AddCompetitionRespVO.FileRespVO> filesRespVO = CompetitionConvert.INSTANCE.convertListOfFiles(reqVO.getOfficial_files()).stream().map(file -> {
//            file.setCompetition_id(competitionId);
//            competitionService.addFile(file);
//            return CompetitionConvert.INSTANCE.convert(file);
//        }).toList();
//        List<AddCompetitionRespVO.FileRespVO> videosRespVO = CompetitionConvert.INSTANCE.convertListOfVideos(reqVO.getPromotional_video_files()).stream().map(video -> {
//            video.setCompetition_id(competitionId);
//            competitionService.addVideo(video);
//            return CompetitionConvert.INSTANCE.convertVideoRespVo(video);
//        }).toList();

        AddCompetitionRespVO respVO = CompetitionConvert.INSTANCE.convert(competition);

        return CommonResult.success(respVO);
    }

    @SneakyThrows
    @Transactional
    @PostMapping("/uploadOfficialFile")
    public CommonResult<SplitFileRespVO> uploadComOfficialFiles(@ModelAttribute @Valid UploadComFileReqVO reqVO) {
        long size = FileUtil.chunksSize(reqVO);
        if (size > 10 * 1024 * 1024)
            return CommonResult.error(500, "文件大小不能超过10MB");

        String fileType = FileUtil.getExtensionByFileName(reqVO.getName());
        if (!StringUtils.equalsAny(fileType, ".doc", ".docx", ".pdf", ".xls", ".xlsx"))
            return CommonResult.error(500, "文件类型不支持 -> " + fileType);

        if (competitionService.findByCompetitionCode(reqVO.getCompetition_code()) == null)
            return CommonResult.error(500, "赛事不存在");

        reqVO.setPath(buildComOfficialFilePath(reqVO.getCompetition_code()) + reqVO.getUid() + fileType)
                .setUrl(buildUrl(reqVO.getCompetition_code(), reqVO.getUid()))
                .setType(fileType);
        return CommonResult.success(competitionService.uploadFile(reqVO));
    }

    @SneakyThrows
    @Transactional
    @PostMapping("/uploadContestantsFile")
    public CommonResult<SplitFileRespVO> uploadComContestantsFiles(@ModelAttribute @Valid UploadComFileReqVO reqVO) {
        long size = FileUtil.chunksSize(reqVO);
        if (size > 10 * 1024 * 1024)
            return CommonResult.error(500, "文件大小不能超过10MB");

        String fileType = FileUtil.getExtensionByFileName(reqVO.getName());
        if (!StringUtils.equalsAny(fileType, ".doc", ".docx", ".pdf", ".xls", ".xlsx"))
            return CommonResult.error(500, "文件类型不支持 -> " + fileType);

        if (competitionService.findByCompetitionCode(reqVO.getCompetition_code()) == null)
            return CommonResult.error(500, "赛事不存在");

        reqVO.setPath(buildComOfficialFilePath(reqVO.getCompetition_code()) + reqVO.getUid() + fileType)
                .setUrl(buildUrl(reqVO.getCompetition_code(), reqVO.getUid()))
                .setType(fileType);
        return CommonResult.success(competitionService.uploadFile(reqVO));
    }

    @GetMapping("/getContestantsFile/{code}/{uid}")
    public CommonResult<String> getComContestantsFiles(@PathVariable("code") String code, @PathVariable("uid") String uid) {
        return CommonResult.success("download/"+code+"/"+uid);
    }

    @GetMapping("/getCompetitionFile/{code}/{uid}")
    public CommonResult<String> getComOfficialFiles(@PathVariable("code") String code, @PathVariable("uid") String uid) {
        return CommonResult.success("download/"+code+"/"+uid);
    }

//    @SneakyThrows
//    @Transactional
//    @PostMapping("/uploadVideo")
//    public CommonResult<SplitFileRespVO> uploadVideo(@ModelAttribute @Valid UploadComFileReqVO reqVO) {
//        long size = FileUtil.chunksSize(reqVO);
//        if (size > 500 * 1024 * 1024)
//            return CommonResult.error(500, "文件大小不能超过500MB");
//
//        String fileType = FileUtil.getExtensionByFileName(reqVO.getName());
//        if (!FileUtil.isVideo(FileUtil.getMineType(reqVO.getName())))
//            return CommonResult.error(500, "文件类型不支持 -> " + fileType);
//
//        if (competitionService.findByCompetitionCode(reqVO.getCompetitionCode()) != null)
//            return CommonResult.error(500, "赛事不存在");
//
//        reqVO.setPath(buildComOfficialFilePath(reqVO.getCompetitionCode()) + reqVO.getUid() + "." + fileType)
//                .setUrl(buildUrl(reqVO.getCompetitionCode()) + reqVO.getUid());
//        return CommonResult.success(competitionService.uploadFile(reqVO));
//    }

    @Transactional
    @PostMapping("/delect")
    public CommonResult<DelectCompetitionRespVO> delectCompetition(@RequestBody @Valid DelectCompetitionReqVO reqVO) {
        if (competitionService.findByCompetitionCode(reqVO.getCode()) == null)
            return CommonResult.error(404, "赛事不存在!");

        var respVO = CompetitionConvert.INSTANCE.convert(reqVO);
        respVO.setDelectCompetitionNum(competitionService.deleteCompetitionByCompetitionCode(reqVO.getCode()));
        respVO.setDelectTime(LocalDateTime.now());
        return CommonResult.success(respVO);
    }

    @Transactional
    @PostMapping("/updateCompetitionStatus")
    public CommonResult<UpdateCStatusRespVO> updateCompetitionStatus(@RequestBody @Valid UpdateCStatusReqVO reqVO) {
        UpdateCStatusRespVO respVO = new UpdateCStatusRespVO();
        reqVO.getCompetitions().forEach(c ->
                respVO.add(c.code(), competitionService.updateStatus(c.code(), c.status()))
        );
        return CommonResult.success(respVO);
    }

    @Transactional(readOnly = true)
    @GetMapping("/get")
    public CommonResult<GetCompetitionRespVO> getCompetition(@RequestParam("code") @NotBlank(message = "赛事编号不能为空") String code) {
        Competition competition;
        if ((competition = competitionService.findByCompetitionCode(code)) == null) {
            return CommonResult.error(404,"赛事编号不存在!");
        }

//        String competitionId = competition.getId();

        var respVO = CompetitionConvert.INSTANCE.convertGetCompetitionRespVO(competition);
//        respVO.setStages(competitionService.selectListOfStageByCompetitionID(competitionId).stream()
//                .map(CompetitionConvert.INSTANCE::convertGetStage).toList());
//
//        respVO.setOfficial_files(competitionService.selectListOfFileByCompetitionCode(competitionId).stream()
//                .map(CompetitionConvert.INSTANCE::convertGetFile).toList());

//        respVO.setPromotional_video_files(competitionService.selectListOfVideoByCompetitionID(competitionId).stream()
//                .map(CompetitionConvert.INSTANCE::convertGetVideo).toList());

        return CommonResult.success(respVO);
    }

    @Transactional(readOnly = true)
    @GetMapping("/getall")
    public CommonResult<List<Competition>> getCompetition() {
        return CommonResult.success(competitionService.getAllCompetitions());
    }

    @Transactional(readOnly = true)
    @PostMapping("/getPage")
    public CommonResult<PageResult<GetCompetitionRespVO>> getCompetitionPage(@RequestBody @Valid CompetitionPageReqVO reqVO) {
        PageResult<Competition> pageVO = competitionService.getCompetitionPage(reqVO);
        var respVO = CompetitionConvert.INSTANCE.convertPageGetCompetitionRespVO(pageVO);

        return CommonResult.success(respVO);
    }

    @Transactional
    @PostMapping("/updateCStatusPage")
    public CommonResult<PageResult<GetCompetitionRespVO>> updateCStatusPage(@RequestBody @Valid UpdateCStatusPageReqVO reqVO) {
        PageResult<Competition> pageVO = competitionService.getUpdateCStatusPage(reqVO);
        var respVO = CompetitionConvert.INSTANCE.convertPageGetCompetitionRespVO(pageVO);

        return CommonResult.success(respVO);
    }

    @Transactional(readOnly = true)
    @GetMapping("/getCurrentStage")
    public CommonResult<CurrentStageRespVO> getCurrentStage(@Valid CompetitionCodeReqVO reqVO) {
        Competition competition = competitionService.findByCompetitionCode(reqVO.getCompetitionCode());
        if (competition == null)
            return CommonResult.error(404, "赛事不存在");

        CompetitionStage currentStage = competitionService.getCurrentStage(reqVO.getCompetitionCode(), competition.getCurrent_stage());
        return CommonResult.success(CompetitionConvert.INSTANCE.convertCurrentStage(currentStage));
    }

    @Transactional(readOnly = true)
    @GetMapping("/getFileList")
    public CommonResult<List<GetCFileListRespVO>> getFileList(@Valid CompetitionCodeReqVO reqVO) {
        Competition competition = competitionService.findByCompetitionCode(reqVO.getCompetitionCode());
        if (competition == null)
            return CommonResult.error(404, "赛事不存在");
        List<CompetitionOfficialFile> files = competitionService.selectListOfFileByCompetitionCode(competition.getCode());
        List<GetCFileListRespVO> respVOList = files.stream().map(CompetitionConvert.INSTANCE::convertGetCFileListRespVO).toList();

        return CommonResult.success(respVOList);
    }

    @Transactional(readOnly = true)
    @GetMapping("/checkCodeUnique")
    public CommonResult<JSONObject> checkCodeUnique(@RequestParam("code") @NotBlank(message = "赛事编号不能为空") String code) {
        boolean flag = competitionService.findByCompetitionCode(code) == null;
        JSONObject unique = JSONObject.of("code", code, "unique", flag);
        return CommonResult.success(unique);
    }

    @Transactional(readOnly = true)
    @GetMapping("/getUserRegisteredCompetitions")
    public CommonResult<List<UserRegisteredCompetitionRespVO>> getUserRegisteredCompetitions() {
        LoginUserVO loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            return CommonResult.error(ErrorCode.USER_NOT_LOGIN);
        }

        List<CRegistrations> registrations = registrationsService.findByApplicantId(loginUser.getId());
        List<UserRegisteredCompetitionRespVO> result = new ArrayList<>();

        for (CRegistrations registration : registrations) {
            Competition competition = competitionService.findByCompetitionCode(registration.getCompetition_code());
            if (competition != null) {
                UserRegisteredCompetitionRespVO respVO = new UserRegisteredCompetitionRespVO();
                respVO.setRegistrationId(registration.getId());
                respVO.setCompetitionCode(competition.getCode());
                respVO.setCompetitionName(competition.getName());
                respVO.setCompetitionType(competition.getType());
                respVO.setCompetitionLevel(competition.getLevel());
                respVO.setParticipationType(competition.getParticipation_type());
                respVO.setOrganizer(competition.getOrganizer());
                respVO.setDescription(competition.getDescription());
                respVO.setEnrollStartTime(competition.getEnroll_start_time());
                respVO.setEnrollEndTime(competition.getEnroll_end_time());
                respVO.setTeamId(registration.getTeam_id());
                respVO.setTeamName(registration.getName());
                respVO.setRegistrationStatus(registration.getStatus());
                respVO.setRegistrationTime(registration.getCreateTime());
                respVO.setCompetitionStatus(competition.getStatus());
                result.add(respVO);
            }
        }

        return CommonResult.success(result);
    }

    @Transactional
    @PostMapping("/enrollCompetition")
    public CommonResult<RegistrationComReqVO> enrollCompetition(@RequestBody @Valid RegistrationComReqVO reqVO) {
        Competition com = competitionService.findByCompetitionCode(reqVO.getCompetitionCode());
        if (com == null)
            return CommonResult.error(404, "赛事不存在!");
        if (teamService.findById(reqVO.getTeamId()) == null)
            return CommonResult.error(404, "团队不存在!");

        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(com.getEnroll_start_time()))
            return CommonResult.error(400, "报名尚未开始!");
        if (now.isAfter(com.getEnroll_end_time()))
            return CommonResult.error(400, "报名时间已过!");
        if (teamMembersService.findByTeamIdAndUserId(reqVO.getTeamId(), reqVO.getApplicantId()).getRole() != TeamRole.CAPTAIN.getValue())
            return CommonResult.error(500, "团队报名参赛只能由队长申请");

        CRegistrations existingRegistration = registrationsService.findByTeamIdAndCode(reqVO.getTeamId(), reqVO.getCompetitionCode());
        if (existingRegistration != null) {
            return CommonResult.error(400, "该团队已经报名过该赛事!");
        }

        reqVO.setName(teamService.findById(reqVO.getTeamId()).getName());
        registrationsService.addCRegistrations(CRegistrationConvert.INSTANCE.convert(reqVO));
        return CommonResult.success(reqVO);
    }

    @Transactional
    @PostMapping("/entry/register")
    public CommonResult<SimpleRegistrationRespVO> registerIndividualEntry(@RequestBody @Valid SimpleRegistrationReqVO reqVO) {
        LoginUserVO loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            return CommonResult.error(ErrorCode.USER_NOT_LOGIN);
        }

        Competition competition = competitionService.findByCompetitionCode(reqVO.getCompetition_code());
        if (competition == null) {
            return CommonResult.error(404, "赛事不存在!");
        }

        if (!"individual".equals(competition.getParticipation_type())) {
            return CommonResult.error(400, "该赛事不是个人赛!");
        }

        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(competition.getEnroll_start_time())) {
            return CommonResult.error(400, "报名尚未开始!");
        }
        if (now.isAfter(competition.getEnroll_end_time())) {
            return CommonResult.error(400, "报名时间已过!");
        }

        CRegistrations existingRegistration = registrationsService
                .findByApplicantIdAndCompetitionCode(loginUser.getId(), reqVO.getCompetition_code());
        if (existingRegistration != null) {
            return CommonResult.error(400, "您已经报名过该赛事!");
        }

        reqVO.setUser_id(loginUser.getId());
        if (StringUtils.isBlank(reqVO.getName())) {
            reqVO.setName(loginUser.getAccount() + "的个人报名");
        }

        CRegistrations registration = SimpleRegistrationConvert.INSTANCE.convertWithNullTeamId(reqVO);
        CRegistrations result = registrationsService.addCRegistrations(registration);

        SimpleRegistrationRespVO respVO = SimpleRegistrationConvert.INSTANCE.convert(result);
        return CommonResult.success(respVO);
    }

    @GetMapping("/entry/status")
    public CommonResult<RegistrationStatusRespVO> getEntryStatus(
            @RequestParam("competition_code") String competition_code,
            @RequestParam(value = "user_id", required = false) Integer user_id) {
        LoginUserVO loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            return CommonResult.error(ErrorCode.USER_NOT_LOGIN);
        }

        Integer queryUserId = user_id != null ? user_id : loginUser.getId();
        CRegistrations registration = registrationsService
                .findByApplicantIdAndCompetitionCode(queryUserId, competition_code);

        if (registration == null) {
            return CommonResult.success(RegistrationStatusRespVO.notRegistered());
        } else {
            String registrationType = (registration.getTeam_id() == null || StringUtils.isBlank(registration.getTeam_id())) ? "individual" : "team";
            return CommonResult.success(RegistrationStatusRespVO.registered(
                    registration.getId(),
                    registration.getStatus(),
                    registrationType
            ));
        }
    }

    @GetMapping("/enrollment/time-status")
    public CommonResult<EnrollmentTimeStatusRespVO> getEnrollmentTimeStatus(
            @RequestParam("competition_code") String competitionCode) {

        Competition competition = competitionService.findByCompetitionCode(competitionCode);
        if (competition == null) {
            return CommonResult.error(404, "赛事不存在!");
        }

        LocalDateTime now = LocalDateTime.now();
        EnrollmentTimeStatusRespVO respVO = new EnrollmentTimeStatusRespVO();
        respVO.setCompetitionCode(competitionCode);
        respVO.setEnrollStartTime(competition.getEnroll_start_time());
        respVO.setEnrollEndTime(competition.getEnroll_end_time());
        respVO.setCurrentTime(now);

        if (now.isBefore(competition.getEnroll_start_time())) {
            respVO.setStatus("NOT_STARTED");
            respVO.setStatusDescription("报名尚未开始");
            respVO.setCanEnroll(false);
        } else if (now.isAfter(competition.getEnroll_end_time())) {
            respVO.setStatus("ENDED");
            respVO.setStatusDescription("报名时间已过");
            respVO.setCanEnroll(false);
        } else {
            respVO.setStatus("ONGOING");
            respVO.setStatusDescription("报名进行中");
            respVO.setCanEnroll(true);
        }
        respVO.calculateTimeDifferences();
        return CommonResult.success(respVO);
    }

    @Transactional
    @PostMapping("/reviewRegistrations")
    public CommonResult<PageResult<GetCRegistrationsRespVO>> reviewRegistrations(@RequestBody @Valid GetCRegistrationsReqVO reqVO) {
        if (competitionService.findByCompetitionCode(reqVO.getCode()) == null)
            return CommonResult.error(404, "赛事不存在");

        return CommonResult.success(CRegistrationConvert.INSTANCE.convert(registrationsService.selectCRPage(reqVO)));
    }

    private String buildComOfficialFilePath(String ComCode) {
        return CFBasePath+ "/" + ComCode + "/OfficialFiles/";
    }

    private String buildContestantsFilePath(String ComCode) {
        return CFBasePath+ "/" + ComCode + "/ContestantsFiles/";
    }

    private String buildUrl(String code, String uid) {
        return "/api/competition/getCompetitionFile/" + code + FileUtil.FILE_SEPARATORCHAR + uid;
    }

    private void addStageTask(String code, CompetitionStage stage) {
        addTask(code + "-stage-" + stage.getOrder_num() + "-start",
                new StageTask(CompetitionStatus.IN_PROGRESS, stage.getId()),
                stage.getSubmit_start_time());
        addTask(code + "-stage-" + stage.getOrder_num() + "-end",
                new StageTask(CompetitionStatus.ENDED, stage.getId()),
                stage.getJudge_end_time());
    }

    private void addComTask(String code, LocalDateTime start, LocalDateTime end) {
        addTask(code + "-start",
                new ComTask(CompetitionStatus.IN_PROGRESS, code),
                start);
        addTask(code + "-end",
                new ComTask(CompetitionStatus.ENDED, code),
                end);
    }

    private void addTask(String taskId, BaseTask task, LocalDateTime when) {
        removeTask(taskId);

        timer.schedule(task, Date.from(when.atZone(ZoneId.systemDefault()).toInstant()));
        taskMap.put(taskId, task);
    }

    private boolean removeTask(String taskID) {
        if (!taskMap.containsKey(taskID)) return false;

        boolean result = taskMap.get(taskID).cancel();
        if (result)
            taskMap.remove(taskID);
        return result;
    }

    @Getter
    private class ComTask extends BaseTask {
        private final String ComID;

        public ComTask(CompetitionStatus status, String comID) {
            super(status);
            ComID = comID;
        }

        @Override
        public void run() {
            competitionService.updateStatus(ComID, status.getValue());
        }
    }

    @Getter
    private class StageTask extends BaseTask {
        private final String stageID;

        public StageTask(CompetitionStatus status, String stageID) {
            super(status);
            this.stageID = stageID;
        }

        @Override
        public void run() {
            competitionService.updateStageStatus(stageID, status.getValue());
            if (status.equals(CompetitionStatus.IN_PROGRESS)) {
                CompetitionStage stage = competitionService.findStageById(stageID);
                competitionService.updateCurrentStage(stage.getCompetition_code(), stage.getOrder_num());
            }
        }
    }

    @AllArgsConstructor
    private static abstract class BaseTask extends TimerTask {
        protected final CompetitionStatus status; //将要设置的状态
    }
}