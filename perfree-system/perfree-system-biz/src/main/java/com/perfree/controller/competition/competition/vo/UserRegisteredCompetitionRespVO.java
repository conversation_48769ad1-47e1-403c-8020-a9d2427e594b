package com.perfree.controller.competition.competition.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户报名赛事响应VO")
@Data
public class UserRegisteredCompetitionRespVO {
    
    @Schema(description = "报名记录ID")
    private String registrationId;
    
    @Schema(description = "赛事编号")
    private String competitionCode;
    
    @Schema(description = "赛事名称")
    private String competitionName;
    
    @Schema(description = "赛事类型")
    private String competitionType;
    
    @Schema(description = "赛事级别")
    private String competitionLevel;
    
    @Schema(description = "参赛形式")
    private String participationType;
    
    @Schema(description = "主办方")
    private String organizer;
    
    @Schema(description = "赛事描述")
    private String description;
    
    @Schema(description = "报名开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enrollStartTime;
    
    @Schema(description = "报名结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enrollEndTime;
    
    @Schema(description = "团队ID")
    private String teamId;
    
    @Schema(description = "团队名称")
    private String teamName;
    
    @Schema(description = "报名状态", example = "0-待审核，1-已通过，2-被拒绝")
    private Integer registrationStatus;
    
    @Schema(description = "报名时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registrationTime;
    
    @Schema(description = "赛事状态", example = "0-待开始，1-进行中，2-已结束")
    private Integer competitionStatus;
}
