package com.perfree.controller.competition.competition.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CompetitionStatus {
    NOT_STARTED("待开始", 0),
    IN_PROGRESS("进行中", 1),
    ENDED("已结束", 2)
    ;

    private final String key;
    private final int value;

    /**
     * 根据状态码获取对应的枚举值
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static CompetitionStatus fromCode(int code) {
        if (code < 0 || code >= values().length) {
            throw new IllegalArgumentException("无效的状态码: " + code);
        }
        return values()[code];
    }
}
