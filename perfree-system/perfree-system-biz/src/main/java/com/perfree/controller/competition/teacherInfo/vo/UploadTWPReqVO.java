package com.perfree.controller.competition.teacherInfo.vo;

import com.perfree.util.filevo.UploadSplFReqVO;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UploadTWPReqVO extends UploadSplFReqVO {
    @NotNull(message = "用户ID不能为空")
    Integer userId;
}
