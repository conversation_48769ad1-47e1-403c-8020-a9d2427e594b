package com.perfree.controller.competition.teacherInfo.vo;

import com.perfree.annotation.YearRange;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.time.Year;

@Data
public class BaseTIReqVO {
    @NotBlank(message = "请填写完整信息")
    @Size(max = 32, message = "名字不能超过32个字符")
    private String name;

    @NotBlank(message = "请填写完整信息")
    @Size(max = 32, message = "工号长度不能超过32")
    private String work_id;

    @NotBlank(message = "请填写完整信息")
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$", message = "请输入正确的手机号")
    @Size(max = 16)
    private String phone;

    @NotBlank(message = "请填写完整信息")
    @Email(message = "邮件格式不正确")
    @Size(max = 16)
    private String email;

    @NotBlank(message = "请填写完整信息")
    @Size(max = 32, message = "学历不能超过32个字符")
    private String education;

    @NotNull(message = "请填写完整信息")
    @YearRange(min = "1980", maxUseCurrentDate = true, message = "入职年份必须为1980-当前年份")
    private Year entry_year;

    @NotBlank(message = "请填写完整信息")
    @Size(max = 32, message = "职称不能超过32个字符")
    private String position;

    @NotBlank(message = "请填写完整信息")
    @Size(max = 64, message = "任职学校不能超过64个字符")
    private String department;
}
