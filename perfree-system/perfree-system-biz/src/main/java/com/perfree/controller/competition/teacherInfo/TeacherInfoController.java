package com.perfree.controller.competition.teacherInfo;

import com.perfree.commons.common.CommonResult;
import com.perfree.commons.exception.ServiceException;
import com.perfree.commons.utils.FileTypeUtils;
import com.perfree.controller.competition.teacherInfo.vo.GetTIRespVO;
import com.perfree.controller.competition.teacherInfo.vo.UpdateTIReqVO;
import com.perfree.controller.competition.teacherInfo.vo.UpdateTIRespVO;
import com.perfree.controller.competition.teacherInfo.vo.UploadTWPReqVO;
import com.perfree.convert.competition.teacherInfo.TInfoConvert;
import com.perfree.model.SAndTPhoto;
import com.perfree.model.TeacherInfo;
import com.perfree.security.SecurityFrameworkUtils;
import com.perfree.security.vo.LoginUserVO;
import com.perfree.service.competition.teacherInfo.TeacherInfoService;
import com.perfree.util.filevo.SplitFileRespVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.io.file.FileTypeUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.UrlResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@RestController
@Tag(name = "教师用户相关接口")
@RequestMapping("api/competition/teacher")
public class TeacherInfoController {

    @Value("${competition-resources.student&teacher-photo-path}")
    private String TWPPath;

    @Value("${competition-resources.student&teacher-photo-url}")
    private String TWPUrl;

    @Resource
    private TeacherInfoService teacherInfoService;

    @Transactional
    @PreAuthorize("@ss.hasPermission('admin:teacher:update')")
    @PostMapping("/update")
    public CommonResult<UpdateTIRespVO> updateTeacherInfo(@RequestBody @Valid UpdateTIReqVO reqVO) throws NoSuchFieldException, IllegalAccessException {
        if (isNotAdminOrOwner(reqVO.getUserId())) return CommonResult.error(500, "没有权限");
        if (teacherInfoService.findByUserId(reqVO.getUserId()) == null) return CommonResult.error(404, "信息不存在，可能用户并非教师用户");


        boolean result = teacherInfoService.updataTeacherInfo(reqVO);
        return CommonResult.success(new UpdateTIRespVO(result, Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant())));
    }

    @Transactional(readOnly = true)
    @GetMapping("/get")
    public CommonResult<GetTIRespVO> getTeacherInfo(@RequestParam("userId") int userId) {
        TeacherInfo TF = teacherInfoService.findByUserId(userId);
        if (TF == null) CommonResult.error(404, "不存在");

        return CommonResult.success(TInfoConvert.INSTANCE.convertGetTIRespVO(TF));
    }

    @Transactional
    @PreAuthorize("@ss.hasPermission('admin:teacher:update')")
    @PostMapping("/upload_work_photo")
    public CommonResult<SplitFileRespVO> uploadWorkPhoto(@ModelAttribute @Valid UploadTWPReqVO reqVO) throws IOException {
        if (isNotAdminOrOwner(reqVO.getUserId())) CommonResult.error(500, "没有权限");

        long size = reqVO.getFile().getSize();
        if (reqVO.getFile().getSize() > 2 * 1024 * 1024)
            throw new ServiceException(500, "文件大小不能超过2MB");

        String fileType = FileTypeUtil.getType(reqVO.getFile().getInputStream());
        if (!StringUtils.equalsAny(fileType, "jpg", "png"))
            return CommonResult.error(500, "文件类型不支持 -> " + fileType);

        reqVO.setPath(TWPPath+ "/" + reqVO.getUserId() + "." + fileType)
                .setUrl("/api/competition/teacher/getTWP/" + reqVO.getUserId());
        return CommonResult.success(teacherInfoService.updateTeacherPhoto(reqVO));
    }

    @Transactional(readOnly = true)
    @GetMapping("/getTWP/{user_id}")
    public ResponseEntity<org.springframework.core.io.Resource> getTWP(@PathVariable("user_id") int userId) throws IOException {
        SAndTPhoto TWP = teacherInfoService.getTWP(userId);
        if (TWP == null) throw new ServiceException(404, "文件不存在");

        org.springframework.core.io.Resource resource = new UrlResource(Paths.get(TWPPath+ "/" + userId + "." + TWP.getType()).toUri());
        String mediaType = FileTypeUtils.getMineType(resource.getContentAsByteArray());
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(mediaType))
                .header("Content-Disposition", "inline; filename="+ URLEncoder.encode(TWP.getName(), StandardCharsets.UTF_8))
                .body(resource);
    }

    private boolean isNotAdminOrOwner(int userId){
        LoginUserVO loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) return true;

        return !loginUser.getId().equals(userId);
    }
}