package com.perfree.controller.competition.competition.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "简化的个人赛报名响应VO")
@Data
public class SimpleRegistrationRespVO {

    @Schema(description = "报名记录ID", example = "reg-uuid-123")
    private String id;

    @Schema(description = "赛事编号", example = "COMP20250619001")
    private String competition_code;

    @Schema(description = "用户ID", example = "1")
    private Integer user_id;

    @Schema(description = "报名名称", example = "个人报名")
    private String name;

    @Schema(description = "状态", example = "0")
    private Integer status;

    @Schema(description = "创建时间", example = "2025-07-29T10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2025-07-29T10:00:00")
    private LocalDateTime updateTime;
}
