package ${table.packageName}.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.perfree.commons.common.PageResult;
import com.perfree.commons.mapper.BaseMapperX;
import ${table.packageName}.controller.auth.${table.lowerFirstClassName}.vo.*;
import ${table.packageName}.model.${table.className};
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.*;
import com.perfree.commons.utils.DateTimeUtils;

import java.util.List;
import java.util.Objects;

#if(${table.primaryColumn})
    #set ($primaryJavaField = $table.primaryColumn.substring(0,1).toUpperCase() + ${table.primaryColumn.substring(1)})
#end


## 字段模板
#macro(listCondition)
#foreach ($column in $columnList)
    #if (${column.listQueryOperation})
        #set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
        #if (${column.queryType} == "0")
        #if (${column.javaType} == "LocalDateTime")
        if (null != reqVO.get${JavaField}()) {
            lambdaQueryWrapper.ge(${table.className}::get${JavaField}, DateTimeUtils.genStartTime(reqVO.get${JavaField}()));
            lambdaQueryWrapper.le(${table.className}::get${JavaField}, DateTimeUtils.genEndTime(reqVO.get${JavaField}()));
        }
        #else
        lambdaQueryWrapper.eq(null != reqVO.get${JavaField}(), ${table.className}::get${JavaField}, reqVO.get${JavaField}());
        #end
        #end
        #if (${column.queryType} == "1")
        lambdaQueryWrapper.ne(null != reqVO.get${JavaField}(), ${table.className}::get${JavaField}, reqVO.get${JavaField}());
        #end
        #if (${column.queryType} == "2")
        lambdaQueryWrapper.gt(null != reqVO.get${JavaField}(), ${table.className}::get${JavaField}, reqVO.get${JavaField}());
        #end
        #if (${column.queryType} == "3")
        lambdaQueryWrapper.ge(null != reqVO.get${JavaField}(), ${table.className}::get${JavaField}, reqVO.get${JavaField}());
        #end
        #if (${column.queryType} == "4")
        lambdaQueryWrapper.lt(null != reqVO.get${JavaField}(), ${table.className}::get${JavaField}, reqVO.get${JavaField}());
        #end
        #if (${column.queryType} == "5")
        lambdaQueryWrapper.le(null != reqVO.get${JavaField}(), ${table.className}::get${JavaField}, reqVO.get${JavaField}());
        #end
        #if (${column.queryType} == "6")
        lambdaQueryWrapper.like(StringUtils.isNotBlank(reqVO.get${JavaField}()), ${table.className}::get${JavaField}, reqVO.get${JavaField}());
        #end
        #if (${column.queryType} == "7")
        if (reqVO.get${JavaField}() != null && reqVO.get${JavaField}().length > 0 && reqVO.get${JavaField}()[0] != null) {
            lambdaQueryWrapper.ge(${table.className}::get${JavaField}, reqVO.get${JavaField}()[0]);
        }
        if (reqVO.get${JavaField}() != null && reqVO.get${JavaField}().length > 1 && reqVO.get${JavaField}()[1] != null) {
            lambdaQueryWrapper.le(${table.className}::get${JavaField}, reqVO.get${JavaField}()[1]);
        }
        #end
    #end
#end
#end

/**
* @description ${table.comment}
* <AUTHOR>
*/
@Mapper
public interface ${table.className}Mapper extends BaseMapperX<${table.className}> {

    default PageResult<${table.className}> selectPage(${table.className}PageReqVO reqVO) {
        LambdaQueryWrapper<${table.className}> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        #listCondition()
        lambdaQueryWrapper.orderByDesc(${table.className}::get${table.upperFirstPrimaryColumn});
        return selectPage(reqVO, lambdaQueryWrapper);
    }

    default List<${table.className}> listAll() {
        return selectList(new LambdaQueryWrapper<${table.className}>()
            .orderByDesc(${table.className}::get${table.upperFirstPrimaryColumn})
        );
    }

    default List<${table.className}> queryExportData(${table.className}ExportReqVO reqVO){
        LambdaQueryWrapper<${table.className}> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        #listCondition()
        lambdaQueryWrapper.orderByDesc(${table.className}::get${table.upperFirstPrimaryColumn});
        return selectList(lambdaQueryWrapper);
    }

}