<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.perfree.mapper.MenuMapper">

    <select id="menuListByUserId" resultType="com.perfree.model.Menu">
        SELECT pm.* FROM p_menu pm
        INNER JOIN p_role_menu prm ON pm.id = prm.menuId
        INNER JOIN p_user_role pur ON prm.roleId = pur.roleId
        LEFT JOIN p_plugin pp ON pm.pluginId = pp.pluginId
        WHERE pur.userId = #{userId,jdbcType=INTEGER} AND pm.menuType in(0, 1)
          AND (pp.pluginId IS NULL OR pp.status = 1)
        order by pm.seq asc
    </select>
    <select id="getPermissionByUserId" resultType="java.lang.String">
        SELECT pm.perms FROM p_menu pm
        INNER JOIN p_role_menu prm ON pm.id = prm.menuId
        INNER JOIN p_user_role pur ON prm.roleId = pur.roleId
        LEFT JOIN p_plugin pp ON pm.pluginId = pp.pluginId
        WHERE pm.menuType = #{menuType,jdbcType=INTEGER} AND pur.userId = #{userId,jdbcType=INTEGER}
          AND (pp.pluginId IS NULL OR pp.status = 1)
    </select>
    <select id="menuListByAdmin" resultType="com.perfree.model.Menu">
        SELECT pm.* FROM p_menu pm
        LEFT JOIN p_plugin pp ON pm.pluginId = pp.pluginId
        WHERE pm.menuType in(0, 1)
          AND (pp.pluginId IS NULL OR pp.status = 1)
        order by pm.seq asc
    </select>
</mapper>
