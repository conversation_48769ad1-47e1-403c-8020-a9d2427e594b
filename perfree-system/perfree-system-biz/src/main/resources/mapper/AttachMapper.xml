<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.perfree.mapper.AttachMapper">

    <select id="getTypeCount" resultType="com.perfree.controller.auth.adminHome.vo.HomeStatisticRespVO">
        SELECT
            IFNULL(SUM(CASE WHEN type = 'img' THEN num ELSE 0 END),0) AS attachImageTotal,
            IFNULL(SUM(CASE WHEN type = 'video' THEN num ELSE 0 END),0) AS attachVideoTotal,
            IFNULL(SUM(CASE WHEN type = 'audio' THEN num ELSE 0 END),0) AS attachAudioTotal,
            IFNULL(SUM(CASE WHEN type = 'other' THEN num ELSE 0 END),0) AS attachOtherTotal,
            IFNULL(SUM(num),0) AS attachTotal
        FROM (
                 SELECT count(1) as num , type FROM p_attach  GROUP BY type
             ) as subquery
    </select>
    <select id="getAllAttachGroup" resultType="com.perfree.controller.auth.attach.vo.AttachGroupRespVO">
        select attachGroup from  p_attach GROUP BY attachGroup
    </select>
</mapper>
