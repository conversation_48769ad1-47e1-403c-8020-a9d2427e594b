<template>
  <div class="page">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" ref="searchFormRef">
        <el-form-item label="比赛名称">
          <el-input v-model="searchForm.competitionName" placeholder="请输入比赛名称" clearable/>
        </el-form-item>
        <el-form-item label="学生姓名">
          <el-input v-model="searchForm.studentName" placeholder="请输入学生姓名" clearable/>
        </el-form-item>
        <el-form-item label="报名状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待审核" value="待审核"/>
            <el-option label="已审核" value="已审核"/>
            <el-option label="已拒绝" value="已拒绝"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="initList" :icon="Search" v-hasPermission="['admin:competition:query']">查询</el-button>
          <el-button :icon="Refresh" @click="resetSearchForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button :icon="Plus" type="primary" plain @click="handleAdd" v-hasPermission="['admin:competition:create']">我要报名</el-button>
      </el-col>
      <div class="right-tool">
        <el-button :icon="Refresh" circle @click="initList"/>
      </div>
    </el-row>

    <div class="table-box">
      <el-table :data="tableData" style="width: 100%;height:100%;" row-key="id" v-loading="loading">
        <el-table-column prop="competitionName" label="比赛名称" min-width="180" />
        <el-table-column prop="studentName" label="学生姓名" min-width="120" />
        <el-table-column prop="studentId" label="学号" min-width="120" />
        <el-table-column prop="school" label="学校" min-width="180" />
        <el-table-column prop="grade" label="年级" min-width="100" />
        <el-table-column prop="major" label="专业" min-width="150" />
        <el-table-column prop="phone" label="联系电话" min-width="120" />
        <el-table-column prop="email" label="电子邮箱" min-width="180" />
        <el-table-column prop="registrationTime" label="报名时间" min-width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.registrationTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link :icon="Edit" @click="handleUpdate(scope.row)" v-hasPermission="['admin:competition:update']">修改</el-button>
            <el-button size="small" type="success" link :icon="View" @click="handleView(scope.row)" v-hasPermission="['admin:competition:query']">查看</el-button>
            <el-button size="small" type="danger" link :icon="Delete" @click="handleDelete(scope.row)" v-hasPermission="['admin:competition:delete']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>


      <el-pagination
          v-model:current-page="searchForm.pageNo"
          v-model:page-size="searchForm.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total,sizes,prev, pager, next, jumper"
          background
          small
          @change="initList"
          :total="searchForm.total"
      />
    </div>

    <el-dialog v-model="open" :title="title" width="800px" draggable>
      <el-form
          ref="formRef"
          :model="form"
          label-width="120px"
          status-icon
          :rules="rules"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="比赛名称" prop="competitionName">
              <el-select v-model="form.competitionName" placeholder="请选择比赛" style="width: 100%">
                <el-option v-for="item in competitionList" :key="item.id" :label="item.name" :value="item.name"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学生姓名" prop="studentName">
              <el-input v-model="form.studentName" placeholder="请输入学生姓名"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="学号" prop="studentId">
              <el-input v-model="form.studentId" placeholder="请输入学号"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学校" prop="school">
              <el-input v-model="form.school" placeholder="请输入学校名称"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="年级" prop="grade">
              <el-select v-model="form.grade" placeholder="请选择年级" style="width: 100%">
                <el-option label="大一" value="大一"/>
                <el-option label="大二" value="大二"/>
                <el-option label="大三" value="大三"/>
                <el-option label="大四" value="大四"/>
                <el-option label="研究生" value="研究生"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专业" prop="major">
              <el-input v-model="form.major" placeholder="请输入专业名称"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入电子邮箱"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="参赛类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择参赛类型" style="width: 100%">
                <el-option label="个人赛" value="个人赛"/>
                <el-option label="团队赛" value="团队赛"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参赛组别" prop="category">
              <el-select v-model="form.category" placeholder="请选择参赛组别" style="width: 100%">
                <el-option label="本科生组" value="本科生组"/>
                <el-option label="研究生组" value="研究生组"/>
                <el-option label="混合组" value="混合组"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="参赛经历" prop="experience">
          <el-input type="textarea" v-model="form.experience" placeholder="请简要描述您的参赛经历（选填）" :rows="3"/>
        </el-form-item>

        <el-form-item label="相关证明" prop="files">
          <el-upload
              action="/api/upload"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              multiple
              :limit="3"
              :on-exceed="handleExceed"
              :file-list="fileList"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请上传学生证、获奖证书等相关证明文件，最多3个文件
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="报名须知" prop="agreement">
          <el-checkbox v-model="form.agreement">
            我已阅读并同意<a href="#" @click.prevent="showAgreement">《比赛报名须知》</a>
          </el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitForm">提交报名</el-button>
          <el-button @click="open = false; resetForm()">取 消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 报名须知对话框 -->
    <el-dialog v-model="agreementVisible" title="比赛报名须知" width="600px">
      <div class="agreement-content">
        <h3>一、报名资格</h3>
        <p>1. 参赛者必须是在校学生，具有有效的学生身份证明。</p>
        <p>2. 参赛者必须遵守学校相关规定，获得学校同意。</p>
        <h3>二、报名要求</h3>
        <p>1. 所有参赛者必须提供真实有效的学生信息。</p>
        <p>2. 团队参赛需指定一名队长，负责团队相关事宜。</p>
        <p>3. 参赛者需确保比赛时间与课程安排不冲突。</p>
        <h3>三、注意事项</h3>
        <p>1. 报名成功后不得随意更改参赛信息。</p>
        <p>2. 请确保提供的联系方式畅通，以便接收比赛通知。</p>
        <p>3. 参赛者需遵守比赛规则，维护学校形象。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="agreementVisible = false">我已阅读</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Edit, Plus, Refresh, Search, View } from '@element-plus/icons-vue'
import { parseTime } from '@/core/utils/perfree.js'
import { addCompetition, competitionDelApi, competitionGetApi, competitionPageApi, competitionUpdateApi } from '../api/competition.js'
import { reactive, ref } from 'vue'

const searchForm = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0,
  competitionName: '',
  studentName: '',
  status: ''
})

const form = ref({
  id: '',
  competitionName: '',
  studentName: '',
  studentId: '',
  school: '',
  grade: '',
  major: '',
  phone: '',
  email: '',
  type: '',
  category: '',
  experience: '',
  files: [],
  agreement: false
})

const rules = reactive({
  competitionName: [
    { required: true, message: '请选择比赛名称', trigger: 'change' }
  ],
  studentName: [
    { required: true, message: '请输入学生姓名', trigger: 'blur' }
  ],
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ],
  school: [
    { required: true, message: '请输入学校名称', trigger: 'blur' }
  ],
  grade: [
    { required: true, message: '请选择年级', trigger: 'change' }
  ],
  major: [
    { required: true, message: '请输入专业名称', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择参赛类型', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择参赛组别', trigger: 'change' }
  ],
  agreement: [
    { required: true, message: '请阅读并同意报名须知', trigger: 'change' }
  ]
})

const formRef = ref()
const searchFormRef = ref()
let open = ref(false)
let title = ref('')
let tableData = ref([])
let loading = ref(false)
let agreementVisible = ref(false)
let fileList = ref([])
let competitionList = ref([])

// 获取状态标签类型
function getStatusType(status) {
  switch (status) {
    case '已审核':
      return 'success'
    case '待审核':
      return 'warning'
    case '已拒绝':
      return 'danger'
    default:
      return 'info'
  }
}

// 显示报名须知
function showAgreement() {
  agreementVisible.value = true
}

// 文件上传相关方法
function handleRemove(file, fileList) {
  console.log(file, fileList)
}

function handlePreview(file) {
  console.log(file)
}

function handleExceed(files, fileList) {
  ElMessage.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
}

function beforeRemove(file) {
  return ElMessageBox.confirm(`确定移除 ${file.name}？`)
}

// 加载列表
function initList() {
  loading.value = true
  competitionPageApi(searchForm.value).then((res) => {
    if (res.code === 200) {
      tableData.value = res.data.list
      searchForm.value.total = res.data.total
    } else {
      ElMessage.error(res.msg)
    }
    loading.value = false
  })
}

// 重置搜索表单
function resetSearchForm() {
  searchForm.value = {
    pageNo: 1,
    pageSize: 10,
    total: 0,
    competitionName: '',
    studentName: '',
    status: ''
  }
  searchFormRef.value.resetFields()
  initList()
}

// 重置表单
function resetForm() {
  form.value = {
    id: '',
    competitionName: '',
    studentName: '',
    studentId: '',
    school: '',
    grade: '',
    major: '',
    phone: '',
    email: '',
    type: '',
    category: '',
    experience: '',
    files: [],
    agreement: false
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 新增
function handleAdd() {
  resetForm()
  title.value = '新增报名'
  open.value = true
}

// 修改
function handleUpdate(row) {
  resetForm()
  title.value = '修改报名'
  open.value = true
  competitionGetApi(row.id).then((res) => {
    if (res.code === 200) {
      form.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 删除
function handleDelete(row) {
  ElMessageBox.confirm('确定要删除该报名记录吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    competitionDelApi(row.id).then((res) => {
      if (res.code === 200) {
        ElMessage.success('删除成功')
        initList()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(() => {})
}

// 提交表单
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      if (form.value.id) {
        competitionUpdateApi(form.value).then((res) => {
          if (res.code === 200) {
            ElMessage.success('修改成功')
            open.value = false
            resetForm()
            initList()
          } else {
            ElMessage.error(res.msg)
          }
        })
      } else {
        addCompetition(form.value).then((res) => {
          if (res.code === 200) {
            ElMessage.success('新增成功')
            open.value = false
            resetForm()
            initList()
          } else {
            ElMessage.error(res.msg)
          }
        })
      }
    }
  })
}

initList()
</script>

<style scoped>
.page {
  padding: 20px;
}

.search-box {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.right-tool {
  float: right;
}

.table-box {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 