<template>
  <div class="page">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline search-form" ref="searchFormRef">
        <el-form-item label="赛事名称">
          <el-input
              v-model="searchForm.name"
              placeholder="请输入赛事名称"
              clearable
              @keyup.enter="handleSearch"
              @input="handleNameChange"
              style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="赛事编号">
          <el-input
              v-model="searchForm.code"
              placeholder="请输入赛事编号"
              clearable
              @keyup.enter="handleSearch"
              @input="handleCodeChange"
              style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="赛事类型">
          <el-select
              v-model="searchForm.type"
              placeholder="请选择类型"
              clearable
              @change="handleTypeChange"
              style="width: 180px"
          >
            <el-option label="学术竞赛" value="academic" />
            <el-option label="体育赛事" value="sports" />
            <el-option label="创新创业" value="innovation" />
            <el-option label="文化艺术" value="art" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="text" @click="showAdvancedSearch = !showAdvancedSearch">
            {{ showAdvancedSearch ? '收起' : '高级搜索' }}
            <el-icon class="el-icon--right">
              <component :is="showAdvancedSearch ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 高级搜索区域 -->
      <el-collapse-transition>
        <div v-show="showAdvancedSearch" class="advanced-search">
          <el-form :inline="true" :model="advancedSearchForm" class="demo-form-inline">
            <el-form-item label="主办方">
              <el-input
                  v-model="advancedSearchForm.organizer"
                  placeholder="请输入主办方"
                  clearable
                  @keyup.enter="handleSearch"
                  @input="handleOrganizerChange"
                  style="width: 180px"
              />
            </el-form-item>
            <el-form-item label="赛事级别">
              <el-select
                  v-model="advancedSearchForm.level"
                  placeholder="请选择级别"
                  clearable
                  @change="handleLevelChange"
                  style="width: 180px"
              >
                <el-option label="国家级" value="national" />
                <el-option label="省级" value="provincial" />
                <el-option label="市级" value="municipal" />
                <el-option label="校级" value="school" />
                <el-option label="院级" value="college" />
              </el-select>
            </el-form-item>
            <el-form-item label="参赛形式">
              <el-select
                  v-model="advancedSearchForm.participationType"
                  placeholder="请选择参赛形式"
                  clearable
                  @change="handleParticipationTypeChange"
                  style="width: 180px"
              >
                <el-option label="个人赛" value="individual" />
                <el-option label="团体赛" value="team" />
              </el-select>
            </el-form-item>
            <el-form-item label="举办形式">
              <el-select
                  v-model="advancedSearchForm.form"
                  placeholder="请选择举办形式"
                  clearable
                  @change="handleFormChange"
                  style="width: 180px"
              >
                <el-option label="线上" value="online" />
                <el-option label="线下" value="offline" />
              </el-select>
            </el-form-item>
            <el-form-item label="报名时间">
              <el-date-picker
                  v-model="advancedSearchForm.registrationTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  @change="handleRegistrationTimeChange"
              />
            </el-form-item>
            <el-form-item label="负责人电话">
              <el-input
                  v-model="advancedSearchForm.leaderPhone"
                  placeholder="请输入负责人电话"
                  clearable
                  @keyup.enter="handleSearch"
                  @input="handleLeaderPhoneChange"
                  style="width: 180px"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-collapse-transition>

      <!-- 搜索条件标签 -->
      <div class="search-tags" v-if="activeTags.length">
        <el-tag
            v-for="tag in activeTags"
            :key="tag.key"
            closable
            @close="removeTag(tag.key)"
            class="search-tag"
        >
          {{ tag.label }}：{{ tag.value }}
        </el-tag>
        <el-button type="text" @click="clearAllTags">清空筛选</el-button>
      </div>
    </div>

    <!-- 操作按钮栏，右对齐悬浮于表格上方 -->
    <div class="action-bar">
      <el-button type="primary" class="operation-btn" :disabled="!selectedRows.length" @click="batchStatusDialogVisible = true">
        <el-icon><Refresh /></el-icon>批量更改状态
      </el-button>
      <el-button type="primary" class="operation-btn" @click="handleAdd">
        <el-icon><Plus /></el-icon>新增赛事
      </el-button>
      <el-button type="danger" class="operation-btn" :disabled="!selectedRows.length" @click="handleBatchDelete">
        <el-icon><Delete /></el-icon>删除
      </el-button>
      <el-dropdown @command="handleMoreCommand" trigger="click">
        <el-button class="operation-btn">
          更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="refresh">
              <el-icon><Refresh /></el-icon>刷新
            </el-dropdown-item>
            <el-dropdown-item command="export">
              <el-icon><Download /></el-icon>导出数据
            </el-dropdown-item>
            <el-dropdown-item command="notification">
              <el-icon><Bell /></el-icon>发送通知
            </el-dropdown-item>
            <el-dropdown-item command="share">
              <el-icon><Share /></el-icon>分享
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 表格区域 -->
    <div class="table-box">
      <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="赛事名称" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <el-link type="primary" class="table-link" @click="handleView(row)">{{ row.name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="赛事类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)" class="table-tag">{{ getTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" class="table-tag">{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.level)" class="table-tag">{{ getLevelLabel(row.level) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="participationType" label="参赛形式" width="100">
          <template #default="{ row }">
            <el-tag :type="getParticipationTypeTagType(row.participationType)" class="table-tag">{{ getParticipationTypeLabel(row.participationType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="180" />
        <el-table-column prop="endTime" label="结束时间" width="180" />
        <el-table-column prop="description" label="赛事简介" min-width="200">
          <template #default="{ row }">
            <el-tooltip effect="dark" :content="row.description" placement="top">
              <span class="ellipsis-text">{{ row.description }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="赛事编号" width="180" show-overflow-tooltip />
        <el-table-column prop="leaderPhone" label="负责人电话" width="160">
          <template #default="{ row }">
            <el-tooltip effect="dark" :content="row.leaderPhone" placement="top">
              <span>{{ maskPhone(row.leaderPhone) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div style="display: flex; align-items: center; gap: 8px;">
              <el-select :model-value="row.status" @change="val => handleStatusChange(row, val)" size="small" style="width: 100px">
                <el-option label="开始" value="in_progress" />
                <el-option label="结束" value="ended" />
                <el-option label="暂停" value="paused" />
              </el-select>
              <el-button type="primary" size="small" @click="handleView(row)">查看详情</el-button>
            </div>
          </template>
        </el-table-column>
        <template #empty>
          <el-empty description="暂无赛事数据" :image-size="120" />
        </template>
      </el-table>
      <div class="pagination-box">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 列设置对话框 -->
    <el-dialog
        v-model="columnSettingsVisible"
        title="列设置"
        width="400px"
        append-to-body
    >
      <el-checkbox-group v-model="selectedColumns">
        <el-checkbox label="type">赛事类型</el-checkbox>
        <el-checkbox label="status">状态</el-checkbox>
        <el-checkbox label="level">赛事级别</el-checkbox>
        <el-checkbox label="participationType">参赛形式</el-checkbox>
        <el-checkbox label="startTime">开始时间</el-checkbox>
        <el-checkbox label="endTime">结束时间</el-checkbox>
        <el-checkbox label="organizer">主办方</el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="columnSettingsVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveColumnSettings">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="batchStatusDialogVisible" title="批量更改状态" width="320px" :close-on-click-modal="false">
      <el-select v-model="batchStatusValue" placeholder="请选择状态" style="width: 100%; margin-bottom: 24px;">
        <el-option v-for="opt in batchStatusOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
      </el-select>
      <template #footer>
        <el-button @click="batchStatusDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="batchStatusLoading" @click="handleBatchStatusChange">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情弹窗部分 -->
    <el-dialog
        v-model="detailDialogVisible"
        class="unified-dialog"
        title=""
        width="700px"
        append-to-body
        :body-style="{ maxHeight: '60vh', overflowY: 'auto', padding: '0' }"
        :modal="true"
        :close-on-click-modal="false"
        :show-close="false"
    >
      <template #header>
        <div class="dialog-header">
          <el-icon class="header-icon"><Star /></el-icon>
          <span class="header-title">赛事详情</span>
          <el-button class="header-close" type="text" @click="detailDialogVisible = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      <transition name="fade-slide">
        <div class="dialog-body-animate">
          <CompetitionDetailView :detailData="detailData" @show-stage-detail="showStageDetail" />
        </div>
      </transition>
      <template #footer>
        <el-button type="primary" @click="detailDialogVisible = false" style="width: 120px;">关闭</el-button>
      </template>
    </el-dialog>

    <el-dialog
      v-model="stageDetailDialogVisible"
      class="unified-dialog"
      title=""
      width="520px"
      append-to-body
      :modal="true"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <template #header>
        <div class="dialog-header">
          <el-icon class="header-icon"><Document /></el-icon>
          <span class="header-title">{{ getStageLabel(stageDetailData?.stage_type) }}阶段详情</span>
          <el-button class="header-close" type="text" @click="stageDetailDialogVisible = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      <transition name="fade-slide">
        <div v-if="stageDetailData" class="dialog-body-animate">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="阶段">{{ getStageLabel(stageDetailData.stage_type) }}</el-descriptions-item>
            <el-descriptions-item label="举办形式">
              <el-tag :type="stageDetailData.stage_form === 'online' ? 'success' : 'warning'" effect="plain">
                {{ getFormLabel(stageDetailData.stage_form) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="报名时间">
              {{ formatRange(stageDetailData.enroll_start_time, stageDetailData.enroll_end_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="提交时间">
              {{ formatRange(stageDetailData.submit_start_time, stageDetailData.submit_end_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="评审时间">
              {{ formatRange(stageDetailData.judge_start_time, stageDetailData.judge_end_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="负责人">{{ stageDetailData.leader }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{ stageDetailData.leader_phone }}</el-descriptions-item>
            <el-descriptions-item label="地点">{{ stageDetailData.location || '-' }}</el-descriptions-item>
            <el-descriptions-item label="说明" :span="2">
              <span v-if="!showStageRemarkAll">
                {{ stageDetailData.remark?.slice(0, 80) }}
                <span v-if="stageDetailData.remark && stageDetailData.remark.length > 80">
                  ... <a style="color:#409eff;cursor:pointer;" @click="showStageRemarkAll = true">展开</a>
                </span>
              </span>
              <span v-else>
                {{ stageDetailData.remark }} <a style="color:#409eff;cursor:pointer;" @click="showStageRemarkAll = false">收起</a>
              </span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </transition>
      <template #footer>
        <el-button type="primary" @click="stageDetailDialogVisible = false" style="width: 120px;">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import {
  Refresh,
  Setting,
  ArrowUp,
  ArrowDown,
  Plus,
  Delete,
  Download,
  Upload,
  Edit,
  View,
  CopyDocument,
  VideoPause,
  VideoPlay,
  Bell,
  Share,
  Star,
  Trophy,
  Document,
  Close
} from '@element-plus/icons-vue'
import { addCompetition, competitionUpdateApi, getCompetitionList, getCompetitionDetail, competitionDelApi, batchUpdateStatusApi, batchDeleteApi } from '../api/competition'
import ProUploadFile from './CommonUpload/ProUploadFile.vue'
import { useRouter } from 'vue-router'
import { defineProps } from 'vue'
import CompetitionDetailView from './CommonView/CompetitionDetailView.vue'

const router = useRouter()

// 搜索表单数据
const searchForm = reactive({
  name: '',
  code: '',
  type: ''
})


const showAdvancedSearch = ref(false)
const advancedSearchForm = reactive({
  organizer: '',
  level: '',
  participationType: '',
  form: '',
  registrationTimeRange: [],
  leaderPhone: ''
})

// 处理参赛形式选择
const handleParticipationTypeChange = (value) => {
  handleSearch()
}

// 处理举办形式选择
const handleFormChange = (value) => {
  handleSearch()
}

// 处理主办方输入
const handleOrganizerChange = (value) => {
  // 可以在这里添加防抖逻辑，避免频繁搜索
  handleSearch()
}

// 处理负责人电话输入
const handleLeaderPhoneChange = (value) => {
  // 可以在这里添加防抖逻辑，避免频繁搜索
  handleSearch()
}

// 处理赛事级别选择
const handleLevelChange = (value) => {
  handleSearch()
}

// 处理报名时间选择
const handleRegistrationTimeChange = (value) => {
  handleSearch()
}

// 处理赛事类型选择
const handleTypeChange = (value) => {
  handleSearch()
}

// 处理赛事名称输入
const handleNameChange = (value) => {
  // 可以在这里添加防抖逻辑，避免频繁搜索
  handleSearch()
}

// 处理赛事编号输入
const handleCodeChange = (value) => {
  // 可以在这里添加防抖逻辑，避免频繁搜索
  handleSearch()
}

// 搜索条件标签
const activeTags = computed(() => {
  const tags = []
  const dict = {
    name: '赛事名称',
    code: '赛事编号',
    type: '赛事类型',
    organizer: '主办方',
    level: '赛事级别',
    participationType: '参赛形式',
    form: '举办形式',
    registrationTimeRange: '报名时间',
    leaderPhone: '负责人电话'
  }
  Object.entries({ ...searchForm, ...advancedSearchForm }).forEach(([key, value]) => {
    if (value && !(Array.isArray(value) && value.length === 0)) {
      let showValue = value
      if (Array.isArray(value)) {
        showValue = value.join(' 至 ')
      } else if (key === 'type') {
        showValue = getTypeLabel(value)
      } else if (key === 'level') {
        showValue = getLevelLabel(value)
      } else if (key === 'participationType') {
        showValue = getParticipationTypeLabel(value)
      } else if (key === 'form') {
        showValue = getFormLabel(value)
      }
      tags.push({ key, label: dict[key] || key, value: showValue })
    }
  })
  return tags
})

// 移除标签
const removeTag = (key) => {
  if (key in searchForm) {
    searchForm[key] = ''
  } else if (key in advancedSearchForm) {
    if (key === 'registrationTimeRange') {
      advancedSearchForm[key] = []
    } else {
      advancedSearchForm[key] = ''
    }
  }
  handleSearch()
}

// 清空所有标签
const clearAllTags = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  Object.keys(advancedSearchForm).forEach(key => {
    if (key === 'registrationTimeRange') {
      advancedSearchForm[key] = []
    } else {
      advancedSearchForm[key] = ''
    }
  })
  handleSearch()
}

// 表格数据
const tableData = ref([])
const loading = ref(false)
const selectedRows = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取表格数据（只走真实接口）
const getTableData = async () => {
  loading.value = true
  try {
    const params = buildSearchParams()
    const res = await getCompetitionList(params)
    if (res && res.code === 200) {
      tableData.value = res.data.list || []
      total.value = res.data.total || 0
    } else {
      tableData.value = []
      total.value = 0
      ElMessage.error(res?.message || '获取赛事列表失败')
    }
  } catch (error) {
    tableData.value = []
    total.value = 0
    ElMessage.error('获取赛事列表失败')
  } finally {
    loading.value = false
  }
}

const handleSortChange = ({ prop, order }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  getTableData()
}

// 状态切换操作反馈（如无真实接口则提示开发中）
const handleStatusChange = async (row, newStatus) => {
  ElMessage.info('功能开发中，待后端接口对接')
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  Object.keys(advancedSearchForm).forEach(key => {
    if (key === 'registrationTimeRange') {
      advancedSearchForm[key] = []
    } else {
      advancedSearchForm[key] = ''
    }
  })
  handleSearch()
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 全选
const handleSelectAll = (selection) => {
  selectedRows.value = selection
}

// 列设置
const handleColumnSettings = () => {
  columnSettingsVisible.value = true
}

// 保存列设置
const saveColumnSettings = () => {
  ElMessage.success('列设置已保存')
  columnSettingsVisible.value = false
}

// 导出数据（如无真实接口则提示开发中）
const handleExport = () => {
  ElMessage.info('导出功能开发中，待后端接口对接')
}

// 导入数据（如无真实接口则提示开发中）
const handleImport = () => {
  ElMessage.info('导入功能开发中，待后端接口对接')
}

// 刷新数据
const handleRefresh = () => {
  getTableData()
}

// 设置
const handleSetting = () => {
  columnSettingsVisible.value = true
}

// 新增赛事
const handleAdd = () => {
  router.push('/competition/organizer');
}

// 详情弹窗相关
const detailDialogVisible = ref(false)
const detailData = ref({})
const detailLoading = ref(false)

// 赛事详情（只走真实接口）
const handleView = async (row) => {
  detailDialogVisible.value = true
  detailLoading.value = true
  try {
    const res = await getCompetitionDetail(row.code)
    if (res && res.code === 200) {
      detailData.value = res.data
    } else {
      detailData.value = { name: '未找到赛事详情', code: row.code }
      ElMessage.error(res?.message || '获取赛事详情失败')
    }
  } catch (error) {
    detailData.value = { name: '未找到赛事详情', code: row.code }
    ElMessage.error('获取赛事详情失败')
  } finally {
    detailLoading.value = false
  }
}

// 复制赛事
const handleCopy = (row) => {
  ElMessageBox.confirm(`确认复制赛事"${row.name}"吗？`, '复制确认', {
    confirmButtonText: '确认复制',
    cancelButtonText: '取消',
    type: 'info',
  }).then(async () => {
    try {
      // 模拟复制操作
      await new Promise(resolve => setTimeout(resolve, 500))

      // 创建复制的赛事
      const newId = Math.max(...tableData.value.map(item => item.id)) + 1
      const copiedCompetition = {
        ...row,
        id: newId,
        name: `${row.name}_副本`,
        status: 'not_started',
        registrationCount: 0,
        participationType: row.participationType || 'individual',
        form: row.form || 'online',
        code: `COMP${new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14)}${Math.random().toString(36).substr(2, 6).toUpperCase()}`
      }

      tableData.value.unshift(copiedCompetition)
      total.value++

      ElMessage.success('复制成功')
    } catch (error) {
      ElMessage.error('复制失败')
    }
  }).catch(() => {
    // 用户取消复制
  })
}

// 暂停赛事
const handlePause = (row) => {
  ElMessageBox.confirm(`确认暂停赛事"${row.name}"吗？`, '暂停确认', {
    confirmButtonText: '确认暂停',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      row.status = 'paused'
      ElMessage.success('赛事已暂停')
    } catch (error) {
      ElMessage.error('暂停失败')
    }
  }).catch(() => {
    // 用户取消暂停
  })
}

// 恢复赛事
const handleResume = (row) => {
  ElMessageBox.confirm(`确认恢复赛事"${row.name}"吗？`, '恢复确认', {
    confirmButtonText: '确认恢复',
    cancelButtonText: '取消',
    type: 'info',
  }).then(async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      row.status = 'in_progress'
      ElMessage.success('赛事已恢复')
    } catch (error) {
      ElMessage.error('恢复失败')
    }
  }).catch(() => {
    // 用户取消恢复
  })
}

// 发布赛事
const handlePublish = (row) => {
  ElMessageBox.confirm(`确认发布赛事"${row.name}"到系统吗？发布后用户将可以看到并参与该赛事。`, '发布确认', {
    confirmButtonText: '确认发布',
    cancelButtonText: '取消',
    type: 'info',
  }).then(async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      row.status = 'published'
      ElMessage.success('赛事已发布到系统')
    } catch (error) {
      ElMessage.error('发布失败')
    }
  }).catch(() => {
    // 用户取消发布
  })
}

// 下架赛事
const handleUnpublish = (row) => {
  ElMessageBox.confirm(`确认下架赛事"${row.name}"吗？下架后用户将无法看到该赛事。`, '下架确认', {
    confirmButtonText: '确认下架',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      row.status = 'pending'
      ElMessage.success('赛事已下架')
    } catch (error) {
      ElMessage.error('下架失败')
    }
  }).catch(() => {
    // 用户取消下架
  })
}

// 发送通知
const handleNotification = () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要发送通知的赛事')
    return
  }

  ElMessageBox.prompt('请输入通知内容', '发送通知', {
    confirmButtonText: '发送',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入通知内容...'
  }).then(async ({ value }) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      ElMessage.success(`已向 ${selectedRows.value.length} 个赛事的参与者发送通知`)
    } catch (error) {
      ElMessage.error('发送通知失败')
    }
  }).catch(() => {
    // 用户取消发送
  })
}

// 分享赛事
const handleShare = () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要分享的赛事')
    return
  }

  const shareData = selectedRows.value.map(row => ({
    名称: row.name,
    类型: getTypeLabel(row.type),
    参赛形式: getParticipationTypeLabel(row.participationType),
    状态: getStatusLabel(row.status),
    编号: row.code
  }))

  // 生成分享链接或二维码
  const shareUrl = `${window.location.origin}/competition/share?ids=${selectedRows.value.map(row => row.id).join(',')}`

  ElMessageBox.alert(
      `<div>
      <p>分享链接：</p>
      <p style="word-break: break-all; color: #409eff;">${shareUrl}</p>
      <p style="margin-top: 10px;">已选择 ${selectedRows.value.length} 个赛事</p>
    </div>`,
      '分享赛事',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '复制链接',
        callback: () => {
          navigator.clipboard.writeText(shareUrl).then(() => {
            ElMessage.success('链接已复制到剪贴板')
          })
        }
      }
  )
}

// 删除赛事（只走真实接口）
const handleBatchDelete = async () => {
  if (!selectedRows.value.length) return
  const codes = selectedRows.value.map(row => row.code);
  try {
    await ElMessageBox.confirm('确定要删除选中的赛事吗？', '删除确认', { type: 'warning' })
    loading.value = true
    await batchDeleteApi({ codes });
    ElMessage.success('删除成功')
    getTableData()
  } catch (e) {
    if (e !== 'cancel') ElMessage.error('删除失败或已取消')
  } finally {
    loading.value = false
  }
}

// 分页相关
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getTableData()
}

// 工具函数
const getTypeTagType = (type) => {
  const map = {
    academic: 'success',
    sports: 'warning',
    innovation: 'info',
    art: 'danger'
  }
  return map[type] || 'info'
}

const getTypeLabel = (type) => {
  const map = {
    academic: '学术竞赛',
    sports: '体育赛事',
    innovation: '创新创业',
    art: '文化艺术'
  }
  return map[type] || type
}

const getStatusTagType = (status) => {
  const map = {
    draft: 'info',
    pending: 'warning',
    published: 'success',
    not_started: 'info',
    in_progress: 'success',
    paused: 'warning',
    ended: 'danger',
    cancelled: 'danger'
  }
  return map[status] || 'info'
}

const getStatusLabel = (status) => {
  const map = {
    draft: '草稿',
    pending: '待发布',
    published: '已发布',
    not_started: '未开始',
    in_progress: '进行中',
    paused: '已暂停',
    ended: '已结束',
    cancelled: '已取消'
  }
  return map[status] || status
}

const getLevelTagType = (level) => {
  const map = {
    national: 'danger',
    provincial: 'warning',
    municipal: 'info',
    school: 'success'
  }
  return map[level] || 'info'
}

const getLevelLabel = (level) => {
  const map = {
    national: '国家级',
    provincial: '省级',
    municipal: '市级',
    school: '校级',
    college: '院级'
  }
  return map[level] || level
}

const getFormLabel = (form) => {
  const map = {
    online: '线上',
    offline: '线下'
  }
  return map[form] || form
}

const getParticipationTypeLabel = (type) => {
  const map = {
    individual: '个人赛',
    team: '团体赛'
  }
  return map[type] || type
}

const getParticipationTypeTagType = (type) => {
  const map = {
    individual: 'info',
    team: 'success'
  }
  return map[type] || 'info'
}

const getFormTagType = (form) => {
  const map = {
    online: 'success',
    offline: 'warning'
  }
  return map[form] || 'info'
}

const getVisibilityTagType = (visibility) => {
  const map = {
    public: 'success',
    private: 'warning',
    internal: 'info'
  }
  return map[visibility] || 'info'
}

const getVisibilityLabel = (visibility) => {
  const map = {
    public: '公开',
    private: '私有',
    internal: '内部'
  }
  return map[visibility] || visibility
}

// 新增阶段
const addStage = () => {
  form.stages.push({
    name: '',
    type: '',
    signupRange: [],
    submitRange: [],
    reviewRange: [],
    leader: '',
    remark: '',
    files: []
  })
}

// 删除阶段
const removeStage = (idx) => {
  form.stages.splice(idx, 1)
}

// 激活阶段
const activeStages = ref([0])

// 初始化
onMounted(() => {
  getTableData()
})

// 处理更多下拉菜单命令
const handleMoreCommand = (command) => {
  switch (command) {
    case 'refresh':
      handleRefresh();
      break;
    case 'export':
      handleExport();
      break;
    case 'notification':
      handleNotification();
      break;
    case 'share':
      handleShare();
      break;
    default:
      break;
  }
}

const sortField = ref('')
const sortOrder = ref('')

// ... 追加到<script setup>中 ...
const maskPhone = (phone) => {
  if (!phone) return '';
  // 只显示前3后2位，中间用*号
  return phone.replace(/^(\\d{3})\\d{6}(\\d{2})$/, '$1******$2');
};

const batchStatusDialogVisible = ref(false);
const batchStatusValue = ref('');
const batchStatusLoading = ref(false);
const batchStatusOptions = [
  { label: '开始', value: 'in_progress' },
  { label: '结束', value: 'ended' },
  { label: '暂停', value: 'paused' }
];
const handleBatchStatusChange = async () => {
  if (!batchStatusValue.value) {
    ElMessage.warning('请选择要更改的状态');
    return;
  }
  const codes = selectedRows.value.map(row => row.code);
  if (!codes.length) {
    ElMessage.warning('请选择要更改的赛事');
    return;
  }
  batchStatusLoading.value = true;
  try {
    await batchUpdateStatusApi({ codes, status: batchStatusValue.value });
    ElMessage.success('批量更改状态成功');
    batchStatusDialogVisible.value = false;
    batchStatusValue.value = '';
    getTableData();
  } catch (e) {
    ElMessage.error('批量更改状态失败，请重试');
  } finally {
    batchStatusLoading.value = false;
  }
}

// 阶段标签
const getStageLabel = (type) => {
  const map = {
    preliminary: '初赛',
    semiFinal: '半决赛',
    final: '决赛',
    qualification: '资格赛',
    elimination: '淘汰赛',
    round: '轮次赛'
  }
  return map[type] || type
}

const stageDetailDialogVisible = ref(false)
const stageDetailData = ref(null)
function showStageDetail(row) {
  stageDetailData.value = row
  stageDetailDialogVisible.value = true
}

function formatRange(start, end) {
  if (!start || !end) return '-'
  return `${formatDate(start)} ~ ${formatDate(end)}`
}
function formatDate(val) {
  if (!val) return ''
  if (typeof val === 'string') val = new Date(val)
  return val instanceof Date ? val.toLocaleString() : val
}

const showStageRemarkAll = ref(false)

</script>

<style scoped>
.page {
  padding: 20px;
}

.search-box {
  margin-bottom: 20px;
}

.action-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  background: #fff;
  padding: 12px 0 0 0;
  border-radius: 8px;
  box-shadow: none;
}

.operation-btn {
  min-width: 110px;
  font-weight: 500;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  height: 32px;
  font-size: 13px;
  transition: all 0.3s;
}
.operation-btn .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

.table-box {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-top: 0;
}

:deep(.el-table) {
  margin-top: 20px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
  margin-bottom: 18px;
}

:deep(.el-table .el-button--text) {
  padding: 0 8px;
}

:deep(.el-table .el-button--text + .el-button--text) {
  margin-left: 8px;
}

:deep(.el-dialog) {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__header) {
  padding: 20px;
  margin: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  max-height: 60vh;
  overflow-y: auto;
  padding: 30px 20px;
  scrollbar-width: thin;
  scrollbar-color: #409eff #f5f5f5;
}
:deep(.el-dialog__body::-webkit-scrollbar) {
  width: 8px;
}
:deep(.el-dialog__body::-webkit-scrollbar-thumb) {
  background: #409eff;
  border-radius: 4px;
}
:deep(.el-dialog__body::-webkit-scrollbar-track) {
  background: #f5f5f5;
}

:deep(.el-dialog__footer) {
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 2;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper:focus-within),
:deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-button--primary) {
  --el-button-hover-bg-color: #66b1ff;
  --el-button-hover-border-color: #66b1ff;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #409eff;
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-select-dropdown__item) {
  padding: 0 12px;
  height: 34px;
  line-height: 34px;
}

:deep(.el-select-dropdown__item.selected) {
  color: #409eff;
  font-weight: bold;
}

:deep(.el-select .el-input__inner) {
  height: 32px;
  line-height: 32px;
}

.advanced-search {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #dcdfe6;
}

.search-tags {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.search-tag {
  margin-right: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}

.search-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.search-tag:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
      0 8px 25px rgba(64, 158, 255, 0.15),
      0 0 0 1px rgba(64, 158, 255, 0.2);
  z-index: 1;
}

.search-tag:hover::before {
  opacity: 1;
}

.search-tag:active {
  transform: translateY(-1px) scale(1.02);
  transition: all 0.1s ease;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

:deep(.el-button-group) {
  margin-right: 10px;
}

:deep(.el-button-group .el-button) {
  margin-right: 0;
}

.dialog-form {
  padding: 20px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.el-collapse-item__header {
  font-weight: bold;
  font-size: 15px;
  color: #409eff;
}

.upload-tip {
  color: #909399;
  font-size: 13px;
  margin-top: 4px;
}

.stage-detail-dialog .el-dialog__header {
  padding: 0;
  border-bottom: none;
}
.dialog-header {
  display: flex;
  align-items: center;
  padding: 24px 32px 10px 32px;
  background: linear-gradient(90deg, #e0e7ff 0%, #f0f4ff 100%);
  border-radius: 12px 12px 0 0;
  position: relative;
}
.header-icon {
  font-size: 28px;
  color: #6366f1;
  margin-right: 10px;
}
.header-title {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  flex: 1;
}
.header-close {
  position: absolute;
  right: 18px;
  top: 18px;
  color: #888;
}
.dialog-body-animate {
  padding: 28px 32px 12px 32px;
  background: #fff;
  border-radius: 0 0 12px 12px;
  animation: fadeInUp 0.4s;
}
.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 15px;
}
.info-label {
  color: #6366f1;
  font-weight: 500;
  min-width: 80px;
}
.info-value {
  color: #222;
  font-size: 15px;
}
.info-remark {
  color: #444;
  background: #f4f8ff;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.7;
  word-break: break-all;
  min-width: 120px;
}
.remark-row {
  align-items: flex-start;
}
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.35s cubic-bezier(.4,2,.6,1);
}
.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateY(30px) scale(0.98);
}
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px);}
  to { opacity: 1; transform: translateY(0);}
}

.unified-dialog .el-dialog__header {
  padding: 0;
  border-bottom: none;
}
.unified-dialog .dialog-header {
  display: flex;
  align-items: center;
  padding: 24px 32px 10px 32px;
  background: linear-gradient(90deg, #e0e7ff 0%, #f0f4ff 100%);
  border-radius: 12px 12px 0 0;
  position: relative;
}
.unified-dialog .header-icon {
  font-size: 28px;
  color: #6366f1;
  margin-right: 10px;
}
.unified-dialog .header-title {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  flex: 1;
}
.unified-dialog .header-close {
  position: absolute;
  right: 18px;
  top: 18px;
  color: #888;
}
.unified-dialog .dialog-body-animate {
  padding: 28px 32px 12px 32px;
  background: #fff;
  border-radius: 0 0 12px 12px;
  animation: fadeInUp 0.4s;
}
.unified-dialog .info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 15px;
}
.unified-dialog .info-label {
  color: #6366f1;
  font-weight: 500;
  min-width: 80px;
}
.unified-dialog .info-value {
  color: #222;
  font-size: 15px;
}
.unified-dialog .info-remark {
  color: #444;
  background: #f4f8ff;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.7;
  word-break: break-all;
  min-width: 120px;
}
.unified-dialog .remark-row {
  align-items: flex-start;
}
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.35s cubic-bezier(.4,2,.6,1);
}
.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateY(30px) scale(0.98);
}
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px);}
  to { opacity: 1; transform: translateY(0);}
}
.stage-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(64,158,255,0.08);
  transition: box-shadow 0.2s;
  background: #fafbff;
}
.stage-card:hover {
  box-shadow: 0 6px 24px 0 rgba(64,158,255,0.18);
  border-color: #409eff;
}

.stage-card-flex {
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
}
.stage-card-row {
  width: 100%;
}
.stage-card-left {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
}
.stage-type-label {
  font-weight: bold;
  font-size: 16px;
  color: #6366f1;
  margin-bottom: 4px;
}
.stage-time-row {
  font-size: 13px;
  color: #666;
  margin-bottom: 2px;
}
.stage-card-right {
  flex: 0.9;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  gap: 2px;
  min-width: 90px;
}
.stage-leader-label {
  font-size: 13px;
  color: #888;
  margin-bottom: 2px;
}
.stage-leader-value {
  font-size: 14px;
  color: #222;
  font-weight: 500;
  margin-bottom: 4px;
}
.minimal-stage-card {
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  background: #f8fafc !important;
  box-shadow: none !important;
}
</style> 