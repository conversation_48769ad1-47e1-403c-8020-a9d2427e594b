<template>
  <div class="container competition-visualization-container">
    <div class="main-content">
      <div class="page-title-row">
        <div class="page-title">
          <h1>赛事可视化</h1>
          <div class="page-desc">浏览和报名各类赛事，支持分类、搜索和分页</div>
        </div>
      </div>
      <div class="competition-header-bar">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="category-tabs">
          <el-tab-pane label="全部" name="all"></el-tab-pane>
          <el-tab-pane label="学术竞赛" name="academic"></el-tab-pane>
          <el-tab-pane label="体育赛事" name="sports"></el-tab-pane>
          <el-tab-pane label="创新创业" name="innovation"></el-tab-pane>
          <el-tab-pane label="文化艺术" name="art"></el-tab-pane>
        </el-tabs>
        <div class="search-bar-modern">
          <el-input
            v-model="searchQuery"
            placeholder="请输入赛事名称或编号"
            prefix-icon="el-icon-search"
            @keyup.enter="searchCompetitions"
            class="search-input"
            clearable
          />
          <el-button @click="searchCompetitions" type="primary" class="search-btn">搜索</el-button>
        </div>
      </div>
      <div class="competition-list-modern">
        <el-row :gutter="20">
          <el-col v-for="competition in competitions" :key="competition.id || competition.code" :xs="24" :sm="12" :md="8" :lg="6">
            <el-card class="competition-card-modern" shadow="hover">
              <div class="card-header">
                <span class="competition-name">{{ competition.name }}</span>
                <el-tag :type="getStatusTagType(competition.status)" size="small" class="competition-status">
                  {{ getStatusLabel(competition.status) }}
                </el-tag>
              </div>
              <div class="card-body">
                <div class="info-row">
                  <span class="label">赛事编号：</span>
                  <span class="value">{{ competition.code }}</span>
                </div>
                <div class="info-row">
                  <span class="label">级别：</span>
                  <el-tag :type="getLevelTagType(competition.level)" size="small">
                    {{ getLevelLabel(competition.level) }}
                  </el-tag>
                </div>
                <div class="info-row">
                  <span class="label">参赛形式：</span>
                  <span class="value">{{ getParticipationTypeLabel(competition.participation_type) }}</span>
                </div>
                <div class="info-row">
                  <span class="label">报名开始：</span>
                  <span class="value">{{ competition.enroll_start_time ? competition.enroll_start_time.replace('T', ' ') : '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">报名结束：</span>
                  <span class="value">{{ competition.enroll_end_time ? competition.enroll_end_time.replace('T', ' ') : '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">赛事简介：</span>
                  <span class="value ellipsis-text">{{ competition.description }}</span>
                </div>
              </div>
              <div class="card-footer">
                <el-button size="small" @click="handleView(competition)">查看</el-button>
                <el-button type="primary" size="small" class="rounded-btn" @click="handleRegister(competition)">立即报名</el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <div v-if="competitions.length === 0" class="no-data-modern">
          <div class="empty-icon">
            <i class="el-icon-search"></i>
          </div>
          <div class="empty-text">
            <p>暂无符合条件的赛事</p>
            <p class="empty-hint">请尝试调整搜索条件或筛选条件</p>
          </div>
        </div>
      </div>
      <div class="pagination-modern">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[6, 12, 24, 48]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
      <el-dialog
        :model-value="showPreviewDialog"
        title="赛事信息预览"
        width="800px"
        @close="showPreviewDialog = false"
        destroy-on-close
      >
        <el-descriptions :column="2" border v-if="selectedCompetition">
          <el-descriptions-item label="编号">{{ selectedCompetition.code }}</el-descriptions-item>
          <el-descriptions-item label="名称">{{ selectedCompetition.name }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ getTypeLabel(selectedCompetition.type) }}</el-descriptions-item>
          <el-descriptions-item label="级别">{{ getLevelLabel(selectedCompetition.level) }}</el-descriptions-item>
          <el-descriptions-item label="参赛形式">{{ getParticipationTypeLabel(selectedCompetition.participation_type) }}</el-descriptions-item>
          <el-descriptions-item label="主办方">{{ selectedCompetition.organizer }}</el-descriptions-item>
          <el-descriptions-item label="报名时间">{{ formatRange(selectedCompetition.enroll_start_time, selectedCompetition.enroll_end_time) }}</el-descriptions-item>
        </el-descriptions>
        <div style="margin: 28px 0 0 0;">
          <div style="font-weight: bold; font-size: 16px; margin-bottom: 10px;">赛事官方文件</div>
          <div v-if="selectedCompetition.official_files && selectedCompetition.official_files.length">
            <ul style="padding-left: 0; margin: 0;">
              <li v-for="file in selectedCompetition.official_files" :key="file.file_url" style="list-style: none; margin-bottom: 8px; display: flex; align-items: center;">
                <a :href="file.file_url" target="_blank" style="color:#409EFF; font-size:15px; text-decoration:underline;">{{ file.file_name }}</a>
              </li>
            </ul>
          </div>
          <div v-else style="color:#bbb;">暂无官方文件</div>
        </div>
        <div style="margin: 32px 0 0 0;">
          <div style="font-weight: bold; font-size: 16px; margin-bottom: 8px;">赛事简介</div>
          <div style="background: #f8f8fa; border-radius: 6px; padding: 16px 18px; font-size: 15px; min-height: 60px; white-space: pre-line; word-break: break-all;">{{ selectedCompetition?.description || '暂无简介' }}</div>
        </div>
        <div style="margin: 32px 0 0 0;">
          <div style="font-weight: bold; font-size: 16px; margin-bottom: 8px;">赛事规则</div>
          <div style="background: #f8f8fa; border-radius: 6px; padding: 16px 18px; font-size: 15px; min-height: 60px; white-space: pre-line; word-break: break-all;">{{ selectedCompetition?.rules || '暂无规则' }}</div>
        </div>
        <div style="margin-top: 32px;">
          <el-alert
            v-if="isIndividualCompetition(selectedCompetition)"
            title="本赛事为个人赛，每人仅可提交一份作品"
            type="info"
            show-icon
          />
          <el-alert
            v-else
            title="本赛事为团体赛，每队可提交一份作品，需填写团队成员信息"
            type="success"
            show-icon
          />
        </div>
        <template #footer>
          <el-button @click="showPreviewDialog = false">关闭</el-button>
        </template>
      </el-dialog>
      
      <!-- 团队选择对话框 -->
      <el-dialog
        :model-value="showTeamSelectDialog"
        title="选择团队报名"
        width="600px"
        @close="showTeamSelectDialog = false"
        destroy-on-close
        class="team-select-dialog"
      >
        <div v-if="selectedCompetition" class="team-select-content">
          <!-- 赛事信息 -->
          <div class="competition-info">
            <div class="competition-header">
              <h4>{{ selectedCompetition.name }}</h4>
            </div>
            <div class="competition-code">
              赛事编号：{{ selectedCompetition.code }}
            </div>
            <div class="competition-type">
              <el-tag :type="isIndividualCompetition(selectedCompetition) ? 'info' : 'success'" size="small">
                {{ isIndividualCompetition(selectedCompetition) ? '个人赛' : '团体赛' }}
              </el-tag>
            </div>
          </div>
          
          <!-- 现有团队选择 -->
          <div v-if="userTeams.length > 0" class="team-selection">
            <div class="section-title">
              <span>选择团队</span>
              <span class="team-count">{{ userTeams.length }}个团队</span>
            </div>
            <div class="team-list">
              <div 
                v-for="team in userTeams" 
                :key="team.id || team.name" 
                class="team-item"
                :class="{ 'selected': selectedTeamId === (team.id || team.name) }"
                @click="selectedTeamId = team.id || team.name"
              >
                <div class="team-content">
                  <div class="team-info">
                    <div class="team-header">
                      <h5 class="team-name">{{ team.name }}</h5>
                      <el-tag size="mini" type="success">队长</el-tag>
                    </div>
                    <p class="team-desc">{{ team.description || '暂无描述' }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 团队列表为空时的提醒 -->
          <div v-else class="team-empty-state">
            <div class="empty-icon">
              <i class="el-icon-user-solid"></i>
            </div>
            <div class="empty-text">
              <p>您还没有创建任何团队</p>
              <p class="empty-hint">请先创建团队，然后进行报名</p>
            </div>
          </div>
          
          <!-- 创建新团队 -->
          <div class="create-team-section">
            <div class="section-title">
              <span>创建新团队</span>
            </div>
            <div v-if="!isCreatingNewTeam" class="create-trigger" @click="isCreatingNewTeam = true">
              <i class="el-icon-plus"></i>
              <span>创建新团队</span>
            </div>
            
            <div v-else class="create-form">
              <el-form 
                :model="{ name: newTeamName, description: newTeamDescription }" 
                size="small"
                @submit.prevent="createNewTeam"
                label-width="80px"
              >
                <el-form-item label="团队名称" required>
                  <el-input 
                    v-model="newTeamName" 
                    placeholder="请输入团队名称"
                    maxlength="30"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item label="团队描述" required>
                  <el-input 
                    v-model="newTeamDescription" 
                    type="textarea" 
                    placeholder="请输入团队描述"
                    :rows="3"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" size="small" @click="createNewTeam">
                    <i class="el-icon-check"></i>
                    创建
                  </el-button>
                  <el-button size="small" @click="isCreatingNewTeam = false">
                    取消
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
        
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="showTeamSelectDialog = false">取消</el-button>
            <el-button 
              type="primary" 
              @click="confirmTeamRegistration"
              :disabled="!selectedTeamId"
            >
              确认报名
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getVisualizationPage, getCompetitionDetail, getEntryStatus, registerIndividualEntry, registerTeamEntry, getCompetitionFileList, createTeam, getCaptainTeam } from '../api/Visualization'

const competitions = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(6)
const searchQuery = ref('')
const activeTab = ref('all')

const showPreviewDialog = ref(false)
const selectedCompetition = ref(null)
const previewLoading = ref(false)



const showTeamSelectDialog = ref(false)
const userTeams = ref([])
const selectedTeamId = ref('')
const isCreatingNewTeam = ref(false)
const newTeamName = ref('')
const newTeamDescription = ref('')


const handleView = async (competition) => {
  selectedCompetition.value = competition
  showPreviewDialog.value = true

  try {
    const res = await getCompetitionFileList(competition.code)
    if (res.data && Array.isArray(res.data.data)) {
      selectedCompetition.value.official_files = res.data.data.map(f => ({
        file_name: f.fileName || f.file_name,
        file_url: f.fileUrl || f.file_url
      }))
    } else {
      selectedCompetition.value.official_files = []
    }
  } catch (e) {
    selectedCompetition.value.official_files = []
  }
}


const fetchUserTeams = async () => {
  const token = JSON.parse(localStorage.getItem('token_info') || '{}')
  const userId = token.userId
  
  try {
    const params = {
      id: parseInt(userId) || 1,
      pageNo: 1,
      pageSize: 10
    }
    
    const res = await getCaptainTeam(params)
    
    if (res && res.code === 200) {
      userTeams.value = res.data.list || res.data || []
    } else {
      userTeams.value = []
    }
  } catch (error) {
    console.error('获取团队列表失败:', error)
    userTeams.value = []
  }
}


const showTeamSelection = async (competition) => {
  selectedCompetition.value = competition
  await fetchUserTeams()
  showTeamSelectDialog.value = true
  isCreatingNewTeam.value = false
  selectedTeamId.value = ''
  newTeamName.value = ''
  newTeamDescription.value = ''
}


const createNewTeam = async () => {
  const token = JSON.parse(localStorage.getItem('token_info') || '{}')
  const userId = token.userId
  
  if (!newTeamName.value.trim()) {
    ElMessage.warning('请输入团队名称')
    return
  }
  
  if (!newTeamDescription.value.trim()) {
    ElMessage.warning('请输入团队描述')
    return
  }
  
  try {
    const teamData = {
      userId: parseInt(userId) || 1,
      name: newTeamName.value.trim(),
      description: newTeamDescription.value.trim()
    }
    
    const res = await createTeam(teamData)
    
    if (res && res.code === 200) {
      ElMessage.success('团队创建成功')
      const newTeam = {
        id: `team_${Date.now()}`,
        name: teamData.name,
        description: teamData.description,
        captain: '当前用户'
      }
      
      userTeams.value.push(newTeam)
      selectedTeamId.value = newTeam.id
    } else {
      throw new Error(res?.msg || '团队创建失败')
    }
  } catch (error) {
    console.error('团队创建失败:', error)
    ElMessage.error('团队创建失败: ' + (error.message || '未知错误'))
  }
}


const confirmTeamRegistration = async () => {
  const token = JSON.parse(localStorage.getItem('token_info') || '{}')
  const userId = token.userId
  
  if (!selectedTeamId.value) {
    ElMessage.warning('请选择或创建团队')
    return
  }
  
  try {
    const teamData = {
      competitionCode: selectedCompetition.value.code,
      teamId: selectedTeamId.value,
      applicantId: userId
    }
    
    const res = await registerTeamEntry(teamData)
    
    if (res && res.code === 200) {
      ElMessage.success('团体赛报名成功！')
      showTeamSelectDialog.value = false
      localStorage.setItem('teamId_' + selectedCompetition.value.code, selectedTeamId.value)
    } else {
      throw new Error(res?.msg || '团体赛报名失败')
    }
  } catch (error) {
    console.error('团体赛报名失败:', error)
    ElMessage.error('团体赛报名失败: ' + (error.message || '未知错误'))
  }
}


const handleRegister = async (competition) => {
  const token = JSON.parse(localStorage.getItem('token_info') || '{}')
  const userId = token.userId
  
  try {
    const isIndividual = competition.participation_type === 'individual'
    

    try {
      const statusParams = isIndividual 
        ? { competition_code: competition.code, user_id: userId }
        : { competition_code: competition.code, user_id: userId, team_id: localStorage.getItem('teamId_' + competition.code) }
      
      const statusRes = await getEntryStatus(statusParams)
      if (statusRes.data && statusRes.data.has_registered) {
        ElMessage.info('您已报名该赛事')
        return
      }
    } catch (statusError) {
      // 状态检查失败，继续报名流程
    }
    
    if (isIndividual) {
      try {
        const entryData = {
          competition_code: competition.code,
          user_id: userId,
          name: '个人报名',
          status: 1
        }
        
        const res = await registerIndividualEntry(entryData)
        
        if (res && res.code === 200) {
          ElMessage.success('个人赛报名成功！')
        } else {
          throw new Error(res?.msg || '个人赛报名失败')
        }
      } catch (error) {
        console.error('个人赛报名失败:', error)
        ElMessage.error('个人赛报名失败: ' + (error.message || '未知错误'))
      }
    } else {
      await showTeamSelection(competition)
    }
  } catch (e) {
    ElMessage.error(e.message || '报名失败')
  }
}

const fetchCompetitions = async () => {
  let name = ''
  let code = ''
  if (/^COMP\w*$/i.test(searchQuery.value)) {
    code = searchQuery.value
  } else {
    name = searchQuery.value
  }
  const params = {
    pageNo: currentPage.value,
    pageSize: pageSize.value,
    name,
    code,
    type: activeTab.value === 'all' ? undefined : activeTab.value
  }
  
  try {
    const res = await getVisualizationPage(params)
    
    if (res && res.code === 200 && res.data) {
      competitions.value = res.data.list || []
      total.value = res.data.total || 0
    } else {
      competitions.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取赛事列表失败:', error)
    competitions.value = []
    total.value = 0
  }
}

onMounted(fetchCompetitions)

const handleTabChange = (tabName) => {
  activeTab.value = tabName
  currentPage.value = 1
  fetchCompetitions()
}
const searchCompetitions = () => {
  currentPage.value = 1
  fetchCompetitions()
}
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  fetchCompetitions()
}
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchCompetitions()
}

const getStatusLabel = (status) => {
  const map = {
    0: '未开始',
    1: '进行中',
    2: '已结束',
    'not_started': '未开始',
    'in_progress': '进行中',
    'ended': '已结束'
  }
  return map[status] || status
}
const getLevelLabel = (level) => {
  const map = {
    national: '国家级',
    provincial: '省级',
    municipal: '市级',
    school: '校级',
    college: '院级'
  }
  return map[level] || level
}

// 状态标签样式映射
const getStatusTagType = (status) => {
  const map = {
    0: 'info',
    1: 'success',
    2: 'danger',
    'not_started': 'info',
    'in_progress': 'success',
    'ended': 'danger'
  }
  return map[status] || 'info'
}
// 级别标签样式映射
const getLevelTagType = (level) => {
  const map = {
    national: 'success',
    provincial: 'warning',
    municipal: 'info',
    school: 'success',
    college: 'primary'
  }
  return map[level] || 'info'
}
const getFormLabel = (form) => {
  const map = {
    individual: '个人',
    team: '团队'
  }
  return map[form] || form
}

const getTypeLabel = (type) => {
  return { academic: '学术竞赛', sports: '体育赛事', innovation: '创新创业', art: '文化艺术' }[type] || type
}

const getParticipationTypeLabel = (type) => {
  return { individual: '个人赛', team: '团体赛' }[type] || type
}

const formatRange = (start, end) => {
  if (!start || !end) return '-'
  return `${formatDate(start)} ~ ${formatDate(end)}`
}

const formatDate = (val) => {
  if (!val) return ''
  const d = typeof val === 'string' ? new Date(val) : val
  return d instanceof Date ? d.toLocaleString() : val
}


const isIndividualCompetition = (competition) => {
  if (!competition) return false
  return competition.participation_type === 'individual'
}
</script>

<style scoped>
.container.competition-visualization-container {
  max-width: 2000px;
  margin: 0 auto;
  min-height: calc(100vh - 64px);
  background-color: #fff;
  padding: 24px 24px 32px 24px;
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
}
.main-content {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  margin-left: 0;
  padding: 24px 16px 0 16px;
}
.page-title-row {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-bottom: 18px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
  background: none;
  flex-wrap: wrap;
}
.page-title h1 {
  font-size: 22px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 0;
  letter-spacing: 1px;
  line-height: 1.2;
}
.page-desc {
  color: #909399;
  font-size: 14px;
  margin-top: 4px;
  margin-bottom: 0;
}
.competition-header-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 18px;
  flex-wrap: wrap;
}
.category-tabs {
  flex: 1 1 auto;
  min-width: 220px;
}
.search-bar-modern {
  display: flex;
  align-items: center;
  gap: 8px;
}
.search-input {
  width: 240px;
}
.search-btn {
  border-radius: 8px;
  font-weight: bold;
  font-size: 15px;
  letter-spacing: 1px;
  box-shadow: 0 2px 8px rgba(64,158,255,0.10);
  border: none;
  padding: 0 18px;
}
.competition-list-modern {
  min-height: 200px;
  margin-bottom: 16px;
}
.competition-card-modern {
  border-radius: 14px;
  background: #fff;
  border: 1.5px solid #f0f0f0;
  margin-bottom: 18px;
  transition: box-shadow 0.2s, border 0.2s, background 0.2s, transform 0.15s;
  box-shadow: 0 2px 8px rgba(64,158,255,0.06);
}
.competition-card-modern:hover {
  box-shadow: 0 6px 24px rgba(64,158,255,0.13);
  background: #f5faff;
  border: 1.5px solid #409eff;
  transform: translateY(-2px) scale(1.02);
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-light);
}
.competition-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--el-color-primary);
  flex: 1;
  margin-right: 8px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.competition-status {
  flex-shrink: 0;
}
.card-body {
  flex: 1;
  margin-bottom: 12px;
}
.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}
.label {
  color: var(--el-text-color-secondary);
  min-width: 60px;
  flex-shrink: 0;
}
.value {
  color: var(--el-text-color-primary);
  font-weight: 500;
  flex: 1;
  word-break: break-all;
}
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-light);
  gap: 8px;
}
.card-footer .el-button {
  flex: 1;
  max-width: 80px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 1px;
  box-shadow: 0 2px 8px rgba(64,158,255,0.10);
  border: none;
  padding: 0 14px;
}
.card-footer .el-button:not(.el-button--primary) {
  background: #f4f6fa;
  color: #333;
  transition: background 0.2s;
}
.card-footer .el-button:not(.el-button--primary):hover {
  background: #e0e6ed;
  color: #222;
}
.ellipsis-text {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
.no-data-modern {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
  background: #fafafa;
  border-radius: 8px;
  margin: 20px 0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-text p {
  margin-bottom: 8px;
  font-size: 14px;
}

.empty-hint {
  font-size: 12px;
  color: #c0c4cc;
}

.pagination-modern {
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}
@media (max-width: 900px) {
  .container.competition-visualization-container {
    flex-direction: column;
    padding: 12px 4px;
  }
  .main-content {
    margin-left: 0;
    padding: 12px 4px 0 4px;
  }
  .competition-header-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    margin-bottom: 12px;
  }
  .competition-list-modern {
    margin-bottom: 8px;
  }
}


.team-select-dialog .el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  padding: 20px 24px 0;
  border-radius: 8px 8px 0 0;
}

.team-select-dialog .el-dialog__header .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.team-select-dialog .el-dialog__body {
  padding: 20px 24px;
}

.team-select-dialog .el-dialog__footer {
  padding: 0 24px 20px;
}

.team-select-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.competition-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  margin-bottom: 20px;
  position: relative;
}

.competition-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.competition-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.competition-code {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.competition-type {
  position: absolute;
  top: 16px;
  right: 16px;
}

.team-selection {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.team-count {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.team-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 240px;
  overflow-y: auto;
  padding-right: 4px;
}

.team-item {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.team-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64,158,255,0.1);
}

.team-item.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.team-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.team-info {
  flex: 1;
}

.team-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.team-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.team-desc {
  font-size: 12px;
  color: #606266;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.create-team-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.create-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background: #fff;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-trigger:hover {
  background: #f5f7fa;
  border-color: #409eff;
}

.create-trigger i {
  font-size: 16px;
  color: #409eff;
  margin-right: 6px;
}

.create-trigger span {
  font-size: 14px;
  color: #303133;
}

.create-form {
  padding-top: 16px;
  border-top: 1px dashed #e9ecef;
  margin-top: 16px;
}

.create-form .el-form {
  margin-top: 0;
}

.create-form .el-form-item {
  margin-bottom: 16px;
}

.create-form .el-form-item__label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.create-form .el-form-item__content {
  display: flex;
  align-items: flex-start;
}

.create-form .el-form-item__content .el-input,
.create-form .el-form-item__content .el-textarea {
  flex: 1;
  width: 100%;
}

.create-form .el-form-item__content .el-button {
  margin-left: 8px;
  margin-top: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

/* 滚动条样式 */
.team-list::-webkit-scrollbar {
  width: 6px;
}

.team-list::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

.team-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.team-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 团队选择区域样式优化 */
.team-selection {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.team-count {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.team-empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;
}

.team-empty-state .empty-icon {
  font-size: 36px;
  margin-bottom: 12px;
  color: #c0c4cc;
}

.team-empty-state .empty-text p {
  margin-bottom: 6px;
  font-size: 14px;
}

.team-empty-state .empty-hint {
  font-size: 12px;
  color: #c0c4cc;
}
</style>
