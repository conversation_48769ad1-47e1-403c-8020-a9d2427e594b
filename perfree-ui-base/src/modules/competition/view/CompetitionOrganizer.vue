<template>
  <div class="page">
    <div class="center-content">
      <el-steps :active="activeStep" finish-status="success" align-center style="margin-bottom: 32px; margin-top: 40px;">
        <el-step title="赛事基本信息" />
        <el-step title="赛事阶段设计" />
        <el-step title="赛事官方文件" />
        <el-step title="预览与提交" />
      </el-steps>
      <el-divider />
      <div v-if="activeStep === 0" class="step-content">
        <el-form :model="form" :rules="basicFormRules" ref="basicFormRef" label-width="120px">
          <el-form-item label="赛事编号" required>
            <el-input
                v-model="form.code"
                readonly
                style="max-width: 350px;"
                @focus="onCodeEditAttempt"
                @input="onCodeEditAttempt"
            >
              <template #suffix>
                <el-tooltip content="编号由系统自动生成，不可修改" placement="top">
                  <el-icon style="color: #bfbfbf; margin-right: 8px;"><Lock /></el-icon>
                </el-tooltip>
                <el-tooltip content="点击复制编号" placement="top">
                  <el-icon
                    style="cursor: pointer;"
                    @click="copyCode"
                  ><DocumentCopy /></el-icon>
                </el-tooltip>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="赛事名称" required><el-input v-model="form.name" /></el-form-item>
          <el-form-item label="赛事类型" required>
            <el-select v-model="form.type" placeholder="请选择类型">
              <el-option label="学术竞赛" value="academic" />
              <el-option label="体育赛事" value="sports" />
              <el-option label="创新创业" value="innovation" />
              <el-option label="文化艺术" value="art" />
            </el-select>
          </el-form-item>
          <el-form-item label="赛事级别" required>
            <el-select v-model="form.level" placeholder="请选择级别">
              <el-option label="国家级" value="national" />
              <el-option label="省级" value="provincial" />
              <el-option label="市级" value="municipal" />
              <el-option label="校级" value="school" />
              <el-option label="院级" value="college" />
            </el-select>
          </el-form-item>
          <el-form-item label="参赛形式" required>
            <el-select v-model="form.participationType" placeholder="请选择参赛形式">
              <el-option label="个人赛" value="individual" />
              <el-option label="团体赛" value="team" />
            </el-select>
          </el-form-item>
          <el-form-item label="主办方" required><el-input v-model="form.organizer" /></el-form-item>
          <el-form-item label="赛事简介" required><el-input v-model="form.description" type="textarea" /></el-form-item>
          <el-form-item label="赛事规则" required><el-input v-model="form.rules" type="textarea" /></el-form-item>
        </el-form>
      </div>
      <div v-if="activeStep === 1" class="step-content">
        <el-timeline>
          <el-timeline-item
              v-for="(stage, idx) in form.stages"
              :key="idx"
              :timestamp="''"
              placement="top"
              @click="activeStageIdx = idx"
              :class="{ 'is-active': idx === activeStageIdx }"
          >
            <div class="modern-card">
              <div class="stage-move-btns">
                <el-tooltip content="上移" placement="top">
                  <el-button
                      circle
                      size="large"
                      :disabled="idx === 0"
                      @click="() => { moveStageUp(idx); activeStageIdx = idx - 1; }"
                      class="move-btn"
                  >
                    <el-icon><ArrowUp /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="下移" placement="top">
                  <el-button
                      circle
                      size="large"
                      :disabled="idx === form.stages.length - 1"
                      @click="() => { moveStageDown(idx); activeStageIdx = idx + 1; }"
                      class="move-btn ml-2"
                  >
                    <el-icon><ArrowDown /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
              <div class="stage-badge-center">
                {{ getStageLabel(stage.type) }}
              </div>
              <el-form :model="stage" :rules="stageRules" :ref="setStageFormRef(idx)" label-width="120px" class="space-y-3">
                <el-form-item label="阶段类型" required>
                  <el-select v-model="stage.type" placeholder="请选择阶段类型">
                    <el-option label="初赛" value="preliminary" />
                    <el-option label="复赛" value="semifinal" />
                    <el-option label="决赛" value="final" />
                  </el-select>
                </el-form-item>
                <el-form-item label="举办形式" required>
                  <el-select v-model="stage.form" placeholder="请选择举办形式">
                    <el-option label="线上" value="online" />
                    <el-option label="线下" value="offline" />
                  </el-select>
                </el-form-item>
                <el-form-item label="报名时间" required>
                  <el-date-picker
                      v-model="signupRangeArr[idx]"
                      type="datetimerange"
                      start-placeholder="开始"
                      end-placeholder="结束"
                      style="width: 350px;"
                  />
                </el-form-item>
                <el-form-item label="提交时间" required>
                  <el-date-picker
                      v-model="submitRangeArr[idx]"
                      type="datetimerange"
                      start-placeholder="开始"
                      end-placeholder="结束"
                      style="width: 350px;"
                      @change="handleSubmitTimeChange(idx)"
                  />
                  <div v-if="submitTimeWarned[idx] >= 3 && form.stages[idx] && form.stages[idx].submitStart && form.stages[idx].submitEnd && form.stages[idx].signupStart && form.stages[idx].signupEnd &&
                    (form.stages[idx].submitStart < form.stages[idx].signupStart || form.stages[idx].submitEnd > form.stages[idx].signupEnd)"
                    style="color: #e6a23c; font-size: 13px; margin-top: 4px;">
                  提交时间建议在报名时间范围内，但不强制要求。
                 </div>
                </el-form-item>
                <el-form-item label="评审时间">
                  <el-date-picker
                      v-model="reviewRangeArr[idx]"
                      type="datetimerange"
                      start-placeholder="开始"
                      end-placeholder="结束"
                      style="width: 350px;"
                  />
                </el-form-item>
                <el-form-item label="地点">
                  <el-input v-model="stage.location" placeholder="如为线下请填写具体地址" />
                </el-form-item>
                <el-form-item label="负责人姓名" required>
                  <el-input v-model="stage.leader" placeholder="负责人姓名" />
                </el-form-item>
                <el-form-item label="负责人电话" required>
                  <el-input v-model="stage.leaderPhone" placeholder="请输入手机号或固定电话" />
                </el-form-item>
                <el-form-item label="阶段说明" required>
                  <el-input v-model="stage.remark" type="textarea" />
                </el-form-item>
              </el-form>
              <div class="flex flex-col items-center gap-2 mt-4">
                <el-button
                    type="danger"
                    @click="removeStage(idx)"
                    class="stage-action-btn delete-btn"
                >
                  <el-icon class="mr-1"><Delete /></el-icon> 删除阶段
                </el-button>
                <el-button
                    type="primary"
                    @click="saveCurrentStage(idx)"
                    class="stage-action-btn save-btn"
                >
                  <el-icon class="mr-1"><Document /></el-icon> 保存本阶段
                </el-button>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
        <el-button
            v-if="canAddMoreStages"
            type="primary"
            class="operation-btn float-btn mt-4 flex items-center justify-center font-bold text-base px-6 py-3 rounded-xl shadow transition-all duration-200"
            @click="openAddStageDialog"
        >
          <el-icon class="mr-2"><Plus /></el-icon> 添加阶段
        </el-button>
        <el-dialog v-model="showAddStageDialog" title="选择要添加的阶段类型" width="350px" :close-on-click-modal="false" class="centered-dialog add-stage-dialog">
          <el-radio-group
              v-model="selectedStageType"
              class="flex justify-center items-center gap-8 py-4"
          >
            <el-radio
                v-for="opt in nextAvailableStageTypes"
                :key="opt.value"
                :label="opt.value"
            >
              <el-icon class="mr-2"><Document /></el-icon> {{ opt.label }}
            </el-radio>
          </el-radio-group>
          <template #footer>
            <div class="flex flex-col items-center space-y-2 p-4">
              <div class="flex justify-center space-x-6">
                <el-button @click="showAddStageDialog = false" class="px-8 py-2 rounded-lg text-base">取消</el-button>
                <el-button type="primary" @click="confirmAddStage" :disabled="!selectedStageType" class="px-8 py-2 rounded-lg text-base font-bold">确定</el-button>
              </div>
              <div v-if="!selectedStageType" style="color: #f56c6c; font-size: 13px; margin-top: 4px;">
                请选择阶段类型后再确定
              </div>
            </div>
          </template>
        </el-dialog>
      </div>
      <div v-if="activeStep === 2" class="step-content">
        <el-form :model="form" label-width="120px">
          <el-form-item label="赛事官方文件" class="upload-form-item">
            <div class="upload-center">
              <ProUploadFile
                  v-model:file-list="form.officialFiles"
                  :accept="'.pdf,.doc,.docx'"
                  action="/api/upload/official-file"
                  multiple
                  :limit="5"
              />
              <div class="upload-tip">
                可上传商业计划书等模板文件、赛事通知等，支持PDF、Word文档
              </div>
            </div>
          </el-form-item>
          <el-form-item label="赛事宣传视频" class="upload-form-item">
            <div class="upload-center">
              <VideoUpload v-model:file-list="form.promotionalVideo" />
              <div class="upload-tip">
                可上传赛事宣传视频，支持MP4格式，建议时长不超过5分钟
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="activeStep === 3" class="step-content preview-cards">
        <!-- 赛事基本信息 -->
        <el-card class="preview-card" shadow="hover">
          <div class="preview-title">赛事基本信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="编号">{{ form.code }}</el-descriptions-item>
            <el-descriptions-item label="名称">{{ form.name }}</el-descriptions-item>
            <el-descriptions-item label="类型">{{ getTypeLabel(form.type) }}</el-descriptions-item>
            <el-descriptions-item label="级别">{{ getLevelLabel(form.level) }}</el-descriptions-item>
            <el-descriptions-item label="参赛形式">{{ getParticipationTypeLabel(form.participationType) }}</el-descriptions-item>
            <el-descriptions-item label="主办方">{{ form.organizer }}</el-descriptions-item>
            <el-descriptions-item label="简介">{{ form.description }}</el-descriptions-item>
            <el-descriptions-item label="规则">{{ form.rules }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card class="preview-card" shadow="hover">
          <div class="preview-title">阶段设计</div>
          <div v-for="(stage, idx) in form.stages" :key="idx" class="stage-preview">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="阶段">{{ getStageLabel(stage.type) }}</el-descriptions-item>
              <el-descriptions-item label="举办形式">{{ stage.form ? getFormLabel(stage.form) : '-' }}</el-descriptions-item>
              <el-descriptions-item label="报名时间">{{ formatRange(stage.signupStart, stage.signupEnd) }}</el-descriptions-item>
              <el-descriptions-item label="提交时间">{{ formatRange(stage.submitStart, stage.submitEnd) }}</el-descriptions-item>
              <el-descriptions-item label="评审时间">{{ formatRange(stage.reviewStart, stage.reviewEnd) }}</el-descriptions-item>
              <el-descriptions-item label="负责人">{{ stage.leader }}</el-descriptions-item>
              <el-descriptions-item label="电话">{{ stage.leaderPhone }}</el-descriptions-item>
              <el-descriptions-item label="地点">{{ stage.location }}</el-descriptions-item>
              <el-descriptions-item label="说明">{{ stage.remark }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <el-card class="preview-card" shadow="hover">
          <div class="preview-title">赛事官方文件</div>
          <ul v-if="form.officialFiles && form.officialFiles.length">
            <li v-for="file in form.officialFiles" :key="file.uid">
              <el-icon><Document /></el-icon>
              {{ file.name }}
            </li>
          </ul>
          <div v-else class="empty-tip">暂无上传文件</div>
        </el-card>

        <el-card class="preview-card" shadow="hover">
          <div class="preview-title">赛事宣传视频</div>
          <ul v-if="form.promotionalVideo && form.promotionalVideo.length">
            <li v-for="file in form.promotionalVideo" :key="file.uid">
              <el-icon><VideoPlay /></el-icon>
              {{ file.name }}
            </li>
          </ul>
          <div v-else class="empty-tip">暂无上传视频</div>
        </el-card>
      </div>
      <div class="step-footer">
        <el-button v-if="activeStep > 0" @click="prevStep">上一步</el-button>
        <el-button v-if="activeStep < 3" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="activeStep === 3" type="success" @click="submit" :loading="submitting" :disabled="submitting">提交</el-button>
        <el-button @click="saveDraft">保存草稿</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import ProUploadFile from './CommonUpload/ProUploadFile.vue'
import VideoUpload from './CommonUpload/VideoUpload.vue'
import { Plus, Document, ArrowUp, ArrowDown, Delete, VideoPlay, Lock, DocumentCopy } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { addCompetition } from '../api/competition'
import { gen32UUID } from '@/core/utils/uuid'

function generateCompetitionCode() {
  const now = new Date()
  const pad = n => n.toString().padStart(2, '0')
  const dateStr = `${now.getFullYear()}${pad(now.getMonth()+1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`
  const randStr = Math.random().toString(36).substr(2, 6).toUpperCase()
  return `COMP${dateStr}${randStr}`
}

const activeStep = ref(0)
const activeStageIdx = ref(0)
const basicFormRef = ref(null)
const form = reactive({
  code: '',
  name: '',
  type: '',
  level: '',
  participationType: '',
  organizer: '',
  description: '',
  rules: '',
  officialFiles: [],
  promotionalVideo: [],
  stages: [
    {
      type: 'preliminary',
      form: '',
      reviewStart: null,
      reviewEnd: null,
      signupStart: null,
      signupEnd: null,
      submitStart: null,
      submitEnd: null,
      location: '',
      leader: '',
      leaderPhone: '',
      remark: ''
    }
  ]
})


if (!form.code) {
  form.code = generateCompetitionCode()
}

const nextStep = async () => {
  if (activeStep.value === 0) {

    try {
      await basicFormRef.value.validate()
    } catch (e) {
      ElMessage.error('请完善基本信息')
      return
    }
  } else if (activeStep.value === 1) {
    const { valid, errors } = await validateAllStagesWithOrder()
    if (!valid) {
      const first = errors[0]
      if (first.stageIdx !== null && first.stageIdx !== undefined) {
        activeStageIdx.value = first.stageIdx
        await nextTick()
        const timelineItems = document.querySelectorAll('.el-timeline-item')
        if (timelineItems[first.stageIdx]) {
          timelineItems[first.stageIdx].scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }
      ElMessage.error(first.message)
      return
    }
  }
  if (activeStep.value < 3) activeStep.value++
}
const prevStep = () => {
  if (activeStep.value > 0) activeStep.value--
}

const stageTypeOptions = [
  { label: '初赛', value: 'preliminary' },
  { label: '复赛', value: 'semifinal' },
  { label: '决赛', value: 'final' }
]

const nextAvailableStageTypes = computed(() => {
  const stages = form.stages
  if (stages.length === 0) {
    return [stageTypeOptions[0], stageTypeOptions[1], stageTypeOptions[2]]
  }
  const lastType = stages[stages.length - 1].type
  if (lastType === 'preliminary') {

    const result = []
    if (!stages.find(s => s.type === 'semifinal')) result.push(stageTypeOptions[1])
    if (!stages.find(s => s.type === 'final')) result.push(stageTypeOptions[2])
    return result
  }
  if (lastType === 'semifinal') {

    return !stages.find(s => s.type === 'final') ? [stageTypeOptions[2]] : []
  }

  return []
})

const canAddMoreStages = computed(() => {
  return nextAvailableStageTypes.value.length > 0
})

const showAddStageDialog = ref(false)
const selectedStageType = ref('')

function openAddStageDialog() {
  selectedStageType.value = nextAvailableStageTypes.value.length > 0 ? nextAvailableStageTypes.value[0].value : ''
  showAddStageDialog.value = true
}
function confirmAddStage() {
  if (!selectedStageType.value) return
  addStage(selectedStageType.value)
  showAddStageDialog.value = false
}


const addStage = (type) => {
  if (!type) return
  const newStage = {
    type,
    form: '',
    reviewStart: null,
    reviewEnd: null,
    signupStart: null,
    signupEnd: null,
    submitStart: null,
    submitEnd: null,
    location: '',
    leader: '',
    leaderPhone: '',
    remark: ''
  }

  // 为新阶段设置合理的默认时间
  const now = new Date()
  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000) // 一周后
  const nextWeekEnd = new Date(nextWeek.getTime() + 3 * 24 * 60 * 60 * 1000) // 一周后+3天

  newStage.signupStart = nextWeek
  newStage.signupEnd = nextWeekEnd



  form.stages.push(newStage)
  form.stages = [...form.stages]

  stageFormRefs.value.push(null)
  
  activeStageIdx.value = form.stages.length - 1
}


const removeStage = (idx) => {
  form.stages.splice(idx, 1)

  form.stages = [...form.stages]
  

  stageFormRefs.value.splice(idx, 1)
  
  if (activeStageIdx.value >= form.stages.length) {
    activeStageIdx.value = form.stages.length - 1
  }
}

function saveDraft() {

  const missing = []

  if (!form.name) missing.push('赛事名称未填写')
  if (!form.type) missing.push('赛事类型未选择')
  if (!form.level) missing.push('赛事级别未选择')
  if (!form.participationType) missing.push('参赛形式未选择')
  if (!form.organizer) missing.push('主办方未填写')
  if (!form.description) missing.push('赛事简介未填写')
  if (!form.rules) missing.push('赛事规则未填写')
  

  form.stages.forEach((stage, idx) => {
    console.log(`检查第${idx + 1}阶段:`, stage)
    if (!stage.type) missing.push(`第${idx+1}阶段：阶段类型未填写`)
    if (!stage.form) missing.push(`第${idx+1}阶段：举办形式未填写`)
    if (!stage.signupStart || !stage.signupEnd) missing.push(`第${idx+1}阶段：报名时间未填写`)
    if (!stage.submitStart || !stage.submitEnd) missing.push(`第${idx+1}阶段：提交时间未填写`)
    if (!stage.leader) missing.push(`第${idx+1}阶段：负责人姓名未填写`)
    if (!stage.leaderPhone) missing.push(`第${idx+1}阶段：负责人电话未填写`)
    if (!stage.remark) missing.push(`第${idx+1}阶段：阶段说明未填写`)

  })
  

  const draftData = {
    data: { ...form },
    savedAt: Date.now()
  }
  localStorage.setItem('competitionDraft', JSON.stringify(draftData))
  
  console.log('草稿保存信息:', {
    missingCount: missing.length,
    missingItems: missing,
    stagesCount: form.stages.length,
    formData: form
  })
  
  if (missing.length) {
    ElMessageBox.alert(
      '草稿已保存，但以下内容未填写：<br/>' + missing.join('<br/>'),
      '草稿保存提示',
      { dangerouslyUseHTMLString: true }
    )
  } else {
    ElMessage.success('草稿已保存')
  }
}


const DRAFT_EXPIRE_MS = 7 * 24 * 60 * 60 * 1000
onMounted(() => {
  const draft = localStorage.getItem('competitionDraft')
  if (draft) {
    try {
      const draftObj = JSON.parse(draft)
      if (draftObj.savedAt && (Date.now() - draftObj.savedAt > DRAFT_EXPIRE_MS)) {
        localStorage.removeItem('competitionDraft')
        ElMessage.info('草稿已过期，已自动清理')
      } else {
        Object.assign(form, draftObj.data)
        ElMessage.info('已自动恢复上次草稿')
      }
    } catch (e) {

    }
  }
})
const submitting = ref(false)

const stageFormRefs = ref([])


function setStageFormRef(idx) {
  return el => {
    if (el) {
      while (stageFormRefs.value.length <= idx) {
        stageFormRefs.value.push(null)
      }
      stageFormRefs.value[idx] = el

      if (el.$options && el.$options.rules) {
        Object.keys(el.$options.rules).forEach(field => {
          if (el.$options.rules[field]) {
            el.$options.rules[field].forEach(rule => {
              if (rule.validator) {
                rule.idx = idx
                rule.model = form.stages[idx]
              }
            })
          }
        })
      }
    }
  }
}


const phoneValidator = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入负责人电话'))
  } else {

    const phoneRegex = /^[\+]?[0-9]{0,3}[\-]?(13|14|15|16|17|18|19)[0-9]{9}|0\d{2,3}-\d{7,8}|^0\d{2,3}-\d{7,8}-\d{1,4}$/
    if (!phoneRegex.test(value)) {
      callback(new Error('请输入正确的手机号或固定电话格式'))
    } else {
      callback()
    }
  }
}


function isValidDate(val) {
  return val instanceof Date && !isNaN(val.getTime())
}


function timeOrderValidator(fieldStart, fieldEnd, label, allowEqual = false) {
  return (rule, value, callback) => {
    const stageIdx = rule.idx
    const model = form.stages[stageIdx] || {}
    const start = model[fieldStart]
    const end = model[fieldEnd]
    
    if (!isValidDate(start) || !isValidDate(end)) {
      callback(new Error(`${label}时间无效`))
      return
    }
    if (allowEqual ? start > end : start >= end) {
      callback(new Error(`${label}结束时间必须晚于开始时间`))
      return
    }
    callback()
  }
}


function submitAfterSignupEndValidator(rule, value, callback) {

  const stageIdx = rule.idx
  const model = form.stages[stageIdx] || {}
  
  if (isValidDate(model.signupEnd) && isValidDate(model.submitStart)) {
    if (model.submitStart < model.signupEnd) {
      callback(new Error('提交开始时间必须晚于或等于报名结束时间'))
      return
    }
  }
  if (isValidDate(model.signupEnd) && isValidDate(model.submitEnd)) {
    if (model.submitEnd < model.signupEnd) {
      callback(new Error('提交结束时间必须晚于或等于报名结束时间'))
      return
    }
  }
  callback()
}

function reviewTimeValidator(rule, value, callback) {

  const stageIdx = rule.idx
  const model = form.stages[stageIdx] || {}
  
  if (model.submitEnd && model.reviewStart) {
    if (!isValidDate(model.submitEnd) || !isValidDate(model.reviewStart)) {
      callback(new Error('评审或提交时间无效'))
      return
    }
    if (model.reviewStart < model.submitEnd) {
      callback(new Error('评审开始时间应在提交结束后'))
      return
    }
  }
  callback()
}


function validateStageTypeOrder(stages) {
  const validTransitions = {
    preliminary: ['semifinal', 'final'],
    semifinal: ['final'],
    final: []
  }
  for (let i = 0; i < stages.length; i++) {
    const s = stages[i]
    if (i === 0 && !(s.type === 'preliminary' || s.type === 'final')) {
      return { stageIdx: i, field: 'type', message: '第一个阶段只能是初赛或决赛' }
    }
    if (i > 0) {
      const prevType = stages[i-1].type
      if (!validTransitions[prevType]?.includes(s.type)) {
        return { stageIdx: i, field: 'type', message: `阶段类型顺序错误：${getStageLabel(prevType)}后不能接${getStageLabel(s.type)}` }
      }
    }
  }
  return null
}


function checkStageTimeContinuity(stages) {
  for (let i = 1; i < stages.length; i++) {
    const prev = stages[i-1]
    const curr = stages[i]
    if (isValidDate(prev.reviewEnd) && isValidDate(curr.signupStart) && curr.signupStart < prev.reviewEnd) {
      return { stageIdx: i, field: 'signupStart', message: `第${i+1}阶段报名开始时间应晚于上一阶段评审结束时间` }
    }
  }
  return null
}


async function validateAllStagesWithOrder() {
  const errors = []
  

  while (stageFormRefs.value.length < form.stages.length) {
    stageFormRefs.value.push(null)
  }
  
  // 1. 校验每个阶段表单
  for (let i = 0; i < form.stages.length; i++) {
    const formRef = stageFormRefs.value[i]
    if (formRef) {
      try {
        await formRef.validate()
      } catch (e) {
        Object.keys(e.fields).forEach(field => {
          errors.push({ stageIdx: i, field, message: e.fields[field][0].message })
        })
      }
    } else {
      const stage = form.stages[i]
      if (!stage.type) {
        errors.push({ stageIdx: i, field: 'type', message: '请选择阶段类型' })
      }
      if (!stage.form) {
        errors.push({ stageIdx: i, field: 'form', message: '请选择举办形式' })
      }
      if (!stage.signupStart || !stage.signupEnd) {
        errors.push({ stageIdx: i, field: 'signupStart', message: '请填写报名时间' })
      }
      if (!stage.submitStart || !stage.submitEnd) {
        errors.push({ stageIdx: i, field: 'submitStart', message: '请填写提交时间' })
      }
      if (!stage.leader) {
        errors.push({ stageIdx: i, field: 'leader', message: '请输入负责人姓名' })
      }
      if (!stage.leaderPhone) {
        errors.push({ stageIdx: i, field: 'leaderPhone', message: '请输入负责人电话' })
      }
      if (!stage.remark) {
        errors.push({ stageIdx: i, field: 'remark', message: '请输入阶段说明' })
      }
    }
  }
  

  const orderCheck = validateStageTypeOrder(form.stages)
  if (orderCheck) {
    errors.push(orderCheck)
  }
  

  const continuityError = checkStageTimeContinuity(form.stages)
  if (continuityError) {
    errors.push(continuityError)
  }
  
  return { valid: errors.length === 0, errors }
}


const basicFormRules = {
  name: [{ required: true, message: '请输入赛事名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择赛事类型', trigger: 'change' }],
  level: [{ required: true, message: '请选择赛事级别', trigger: 'change' }],
  participationType: [{ required: true, message: '请选择参赛形式', trigger: 'change' }],
  organizer: [{ required: true, message: '请输入主办方', trigger: 'blur' }],
  description: [{ required: true, message: '请输入赛事简介', trigger: 'blur' }],
  rules: [{ required: true, message: '请输入赛事规则', trigger: 'blur' }]
}


const stageRules = {
  type: [{ required: true, message: '请选择阶段类型', trigger: 'change' }],
  form: [{ required: true, message: '请选择举办形式', trigger: 'change' }],
  signupStart: [
    { required: true, type: 'date', message: '请选择报名开始时间', trigger: 'change' },
    timeOrderValidator('signupStart', 'signupEnd', '报名')
  ],
  signupEnd: [
    { required: true, type: 'date', message: '请选择报名结束时间', trigger: 'change' }
  ],
  submitStart: [
    { required: true, type: 'date', message: '请选择提交开始时间', trigger: 'change' },
    timeOrderValidator('submitStart', 'submitEnd', '提交'),
    submitAfterSignupEndValidator
  ],
  submitEnd: [
    { required: true, type: 'date', message: '请选择提交结束时间', trigger: 'change' },
    submitAfterSignupEndValidator
  ],
  reviewStart: [
    reviewTimeValidator,
    timeOrderValidator('reviewStart', 'reviewEnd', '评审')
  ],
  reviewEnd: [],
  leader: [{ required: true, message: '请输入负责人姓名', trigger: 'blur' }],
  leaderPhone: [{ required: true, validator: phoneValidator, trigger: 'blur' }],
  remark: [{ required: true, message: '请输入阶段说明', trigger: 'blur' }]
}

async function validateAllStages() {
  for (let i = 0; i < stageFormRefs.value.length; i++) {
    const formRef = stageFormRefs.value[i]
    if (formRef) {
      try {
        await formRef.validate()
      } catch (e) {
        return false
      }
    }
  }
  return true
}


function buildCompetitionPayload(form) {
  const formatDate = (date) => {
    if (!date) return null
    const d = new Date(date)
    return d.toISOString().replace(/\.\d{3}Z$/, 'Z')
  }
  

  const mapStageForm = (form) => {
    return form;
  }
  
  // 处理文件UUID
  const processFileUUID = (file) => {
    // 如果uid存在且是32位十六进制字符串,
    if (file.uid && file.uid.length === 32 && /^[0-9a-f]{32}$/.test(file.uid)) {
      return file.uid
    }
    
    // 否则生成新的UUID
    const newUid = gen32UUID()
    return newUid
  }
  
  return {
    code: form.code,
    name: form.name,
    type: form.type,
    level: form.level,
    participation_type: form.participationType, // 修复：使用下划线命名
    organizer: form.organizer,
    description: form.description,
    rules: form.rules,
    stages: (form.stages || []).map(stage => ({
      stage_type: stage.type, // 修复：使用下划线命名
      stage_form: mapStageForm(stage.form), // 修复：映射举办形式
      judge_start_time: formatDate(stage.reviewStart),
      judge_end_time: formatDate(stage.reviewEnd),
      enroll_start_time: formatDate(stage.signupStart),
      enroll_end_time: formatDate(stage.signupEnd),
      submit_start_time: formatDate(stage.submitStart),
      submit_end_time: formatDate(stage.submitEnd),
      location: stage.location,
      leader: stage.leader,
      leader_phone: stage.leaderPhone, // 修复：使用下划线命名
      remark: stage.remark
    })),
    official_files: (form.officialFiles || []).map(f => ({
      file_name: f.name, // 修复：使用下划线命名
      file_url: f.url, // 修复：使用下划线命名
      uid: processFileUUID(f)
    })),
    promotional_video_files: (form.promotionalVideo || []).map(f => ({
      file_name: f.name, // 修复：使用下划线命名
      file_url: f.url, // 修复：使用下划线命名
      uid: processFileUUID(f)
    }))
  }
}

const submit = async () => {
  // 主表单字段全为空时，统一提示
  const mainFields = [form.name, form.type, form.level, form.participationType, form.organizer, form.description, form.rules];
  if (mainFields.every(v => !v || v === '')) {
    ElMessage.error('请填写表单');
    submitting.value = false;
    return;
  }
  if (submitting.value) return
  try {
    await ElMessageBox.confirm('请确认所有信息填写无误，是否提交？', '确认提交', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
  } catch {
    return
  }
  submitting.value = true
  try {
    const { valid, errors } = await validateAllStagesWithOrder()
    if (!valid) {
      const errorHtml = errors.reduce((acc, e) => {
        const stage = e.stageIdx !== null && e.stageIdx !== undefined ? `第${e.stageIdx + 1}阶段：` : ''
        acc += `<li>${stage}${e.message}</li>`
        return acc
      }, '<ul style="padding-left:20px;">') + '</ul>'
      ElMessageBox.alert(
        errorHtml,
        '请检查表单是否有遗漏！',
        { dangerouslyUseHTMLString: true }
      ).then(() => {
        const first = errors[0]
        if (first.stageIdx !== null && first.stageIdx !== undefined) {
          activeStageIdx.value = first.stageIdx
          nextTick(() => {
            const timelineItems = document.querySelectorAll('.el-timeline-item')
            if (timelineItems[first.stageIdx]) {
              timelineItems[first.stageIdx].scrollIntoView({ behavior: 'smooth', block: 'center' })
              timelineItems[first.stageIdx].classList.add('error-highlight')
              setTimeout(() => {
                timelineItems[first.stageIdx].classList.remove('error-highlight')
              }, 2000)
            }
          })
        }
      })
      submitting.value = false
      return
    }
    const payload = buildCompetitionPayload(form)
    const res = await addCompetition(payload)
    if (res && res.code === 200) {
      ElMessage.success('提交成功！')
      localStorage.removeItem('competitionDraft')
    } else {
      throw new Error(res?.msg || '提交失败')
    }
  } catch (error) {
    ElMessage.error(error.message || '提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 阶段时间选择器代理：ref数组+watch方案
const reviewRangeArr = ref(form.stages.map(stage => [stage.reviewStart, stage.reviewEnd]))
const signupRangeArr = ref(form.stages.map(stage => [stage.signupStart, stage.signupEnd]))
const submitRangeArr = ref(form.stages.map(stage => [stage.submitStart, stage.submitEnd]))

// 保证数组长度同步
watch(
    () => form.stages.length,
    () => {
      reviewRangeArr.value = form.stages.map(stage => [stage.reviewStart, stage.reviewEnd])
      signupRangeArr.value = form.stages.map(stage => [stage.signupStart, stage.signupEnd])
      submitRangeArr.value = form.stages.map(stage => [stage.submitStart, stage.submitEnd])
    },
    { immediate: true }
)
// 双向同步：ref数组->form.stages
watch(
    reviewRangeArr,
    (arr) => {
      arr.forEach((val, idx) => {
        if (form.stages[idx]) {
          form.stages[idx].reviewStart = val ? val[0] : null
          form.stages[idx].reviewEnd = val ? val[1] : null
        }
      })
    },
    { deep: true }
)
watch(
    signupRangeArr,
    (arr) => {
      arr.forEach((val, idx) => {
        if (form.stages[idx]) {
          form.stages[idx].signupStart = val ? val[0] : null
          form.stages[idx].signupEnd = val ? val[1] : null
        }
      })
    },
    { deep: true }
)
watch(
    submitRangeArr,
    (arr) => {
      arr.forEach((val, idx) => {
        if (form.stages[idx]) {
          form.stages[idx].submitStart = val ? val[0] : null
          form.stages[idx].submitEnd = val ? val[1] : null
        }
      })
    },
    { deep: true }
)

// ===== 自动联动：报名时间变动自动设置提交时间 =====
watch(
  signupRangeArr,
  (arr) => {
    arr.forEach((val, idx) => {
      if (form.stages[idx]) {
        form.stages[idx].signupStart = val ? val[0] : null
        form.stages[idx].signupEnd = val ? val[1] : null
        // 自动联动：如果提交时间为空，自动设置为报名时间区间的后半段
        if ((!form.stages[idx].submitStart || !form.stages[idx].submitEnd) && val && val[0] && val[1]) {
          const signupStart = val[0]
          const signupEnd = val[1]
          // 提交开始 = 报名开始 + 30%
          // 提交结束 = 报名结束 - 10%
          const total = signupEnd - signupStart
          const submitStart = new Date(signupStart.getTime ? signupStart.getTime() + total * 0.3 : signupStart + total * 0.3)
          const submitEnd = new Date(signupEnd.getTime ? signupEnd.getTime() - total * 0.1 : signupEnd - total * 0.1)
          form.stages[idx].submitStart = submitStart
          form.stages[idx].submitEnd = submitEnd
          if (submitRangeArr.value[idx]) {
            submitRangeArr.value[idx] = [submitStart, submitEnd]
          }
        }
      }
    })
  },
  { deep: true }
)

// 智能时间推算：当用户设置提交时间时，自动设置合理的评审时间 - 已注销
/*
watch(
  () => form.stages.map(s => [s.submitStart, s.submitEnd]),
  (submitRanges) => {
    submitRanges.forEach(([start, end], idx) => {
      const stage = form.stages[idx]
      if (stage && start && end && !stage.reviewStart && !stage.reviewEnd) {
        // 如果提交时间已设置但评审时间未设置，自动设置评审时间
        // 评审开始时间设为提交结束后1小时
        const reviewStart = new Date(end.getTime() + 60 * 60 * 1000)
        // 评审结束时间设为评审开始后3天
        const reviewEnd = new Date(reviewStart.getTime() + 3 * 24 * 60 * 60 * 1000)

        stage.reviewStart = reviewStart
        stage.reviewEnd = reviewEnd
        // 同步到ref数组
        if (reviewRangeArr.value[idx]) {
          reviewRangeArr.value[idx] = [reviewStart, reviewEnd]
        }
      }
    })
  },
  { deep: true }
)
*/

function validateStages() {
  const validTransitions = {
    preliminary: ['semifinal', 'final'],
    semifinal: ['final'],
    final: []
  }
  for (let i = 0; i < form.stages.length; i++) {
    const s = form.stages[i]
    // 基础信息验证
    if (!s.type) return `请填写第${i+1}阶段类型`
    if (!s.form) return `请填写第${i+1}阶段举办形式`
    if (!s.leader) return `请填写第${i+1}阶段负责人姓名`
    if (!s.leaderPhone || !/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(s.leaderPhone)) {
      return `请填写第${i+1}阶段负责人正确手机号`
    }
    if (!s.remark) {
      return `请填写第${i+1}阶段说明`
    }
    // 报名和提交时间必填
    if (!s.signupStart || !s.signupEnd) {
      return `请填写第${i+1}阶段报名时间`
    }
    if (!s.submitStart || !s.submitEnd) {
      return `请填写第${i+1}阶段提交时间`
    }
    // 5. 阶段类型顺序验证
    if (i === 0 && !(s.type === 'preliminary' || s.type === 'final')) {
      return '第一个阶段只能是初赛或决赛'
    }
    if (i > 0) {
      const prevType = form.stages[i-1].type
      if (!validTransitions[prevType]?.includes(s.type)) {
        return `阶段类型顺序错误：${getStageLabel(prevType)}后不能接${getStageLabel(s.type)}`
      }
    }
  }
  return true
}

function getStageLabel(type) {
  return type === 'preliminary' ? '初赛' : type === 'semifinal' ? '复赛' : type === 'final' ? '决赛' : type
}

function onCodeEditAttempt() {
  // 增强：自动恢复原始值
  const original = form.code
  nextTick(() => {
    if (form.code !== original) {
      form.code = original
      ElMessage.error('赛事编号已重置为系统生成值')
    } else {
      ElMessage.error('赛事编号不可更改')
    }
  })
}

function copyCode() {
  if (form.code) {
    navigator.clipboard.writeText(form.code)
      .then(() => {
        ElMessage.success('编号已复制')
      })
      .catch(() => {
        ElMessage.error('复制失败，请手动复制')
      })
  }
}

function formatRange(start, end) {
  if (!start || !end) return '-'
  return `${formatDate(start)} ~ ${formatDate(end)}`
}

function formatDate(val) {
  if (!val) return ''
  if (typeof val === 'string') val = new Date(val)
  return val instanceof Date ? val.toLocaleString() : val
}

function getTypeLabel(type) {
  return { academic: '学术竞赛', sports: '体育赛事', innovation: '创新创业', art: '文化艺术' }[type] || type
}

function getLevelLabel(level) {
  return { national: '国家级', provincial: '省级', municipal: '市级', school: '校级', college: '院级' }[level] || level
}

function getFormLabel(form) {
  return {
    online: '线上',
    offline: '线下'
  }[form] || form
}

function getParticipationTypeLabel(type) {
  return {
    individual: '个人赛',
    team: '团体赛'
  }[type] || type
}

// 阶段排序功能
function moveStageUp(idx) {
  if (idx > 0) {
    // 交换阶段数据
    const temp = form.stages[idx - 1]
    form.stages[idx - 1] = form.stages[idx]
    form.stages[idx] = temp
    
    // 修复：同步交换表单引用
    const tempRef = stageFormRefs.value[idx - 1]
    stageFormRefs.value[idx - 1] = stageFormRefs.value[idx]
    stageFormRefs.value[idx] = tempRef
    
    // 更新验证规则中的索引
    if (stageFormRefs.value[idx - 1]) {
      updateFormRefIndex(stageFormRefs.value[idx - 1], idx - 1)
    }
    if (stageFormRefs.value[idx]) {
      updateFormRefIndex(stageFormRefs.value[idx], idx)
    }
  }
}

function moveStageDown(idx) {
  if (idx < form.stages.length - 1) {
    // 交换阶段数据
    const temp = form.stages[idx + 1]
    form.stages[idx + 1] = form.stages[idx]
    form.stages[idx] = temp
    
    // 修复：同步交换表单引用
    const tempRef = stageFormRefs.value[idx + 1]
    stageFormRefs.value[idx + 1] = stageFormRefs.value[idx]
    stageFormRefs.value[idx] = tempRef
    
    // 更新验证规则中的索引
    if (stageFormRefs.value[idx + 1]) {
      updateFormRefIndex(stageFormRefs.value[idx + 1], idx + 1)
    }
    if (stageFormRefs.value[idx]) {
      updateFormRefIndex(stageFormRefs.value[idx], idx)
    }
  }
}

// 辅助函数：更新表单引用中的索引
function updateFormRefIndex(formRef, newIdx) {
  if (formRef && formRef.$options && formRef.$options.rules) {
    Object.keys(formRef.$options.rules).forEach(field => {
      if (formRef.$options.rules[field]) {
        formRef.$options.rules[field].forEach(rule => {
          if (rule.validator) {
            rule.idx = newIdx
            rule.model = form.stages[newIdx]
          }
        })
      }
    })
  }
}

// 用于每个阶段最多弹三次提交时间温馨提示
const submitTimeWarned = ref([]) // 记录每个阶段已弹次数
function handleSubmitTimeChange(idx) {
  const stage = form.stages[idx]
  if (
    stage &&
    stage.submitStart && stage.submitEnd &&
    stage.signupStart && stage.signupEnd &&
    (stage.submitStart < stage.signupStart || stage.submitEnd > stage.signupEnd)
  ) {
    if (!submitTimeWarned.value[idx]) submitTimeWarned.value[idx] = 0
    if (submitTimeWarned.value[idx] < 3) {
      ElMessage({
        message: '建议提交时间在报名时间范围内，但不强制要求。',
        type: 'warning',
        duration: 3000
      })
      submitTimeWarned.value[idx]++
    }
  } else {
    submitTimeWarned.value[idx] = 0
  }
}

/**
 * 保存当前阶段，仅校验当前阶段，未填项弹窗详细提示并高亮当前阶段，校验通过弹出成功提示
 */
async function saveCurrentStage(idx) {
  const formRef = stageFormRefs.value[idx]
  if (formRef) {
    try {
      await formRef.validate()
      ElMessage.success('本阶段信息已保存')
    } catch (e) {
      // 收集所有错误
      const errorHtml = Object.keys(e.fields).reduce((acc, field) => {
        acc += `<li>${e.fields[field][0].message}</li>`
        return acc
      }, '<ul style="padding-left:20px;">') + '</ul>'
      ElMessageBox.alert(
        errorHtml,
        `请完善第${idx + 1}阶段以下内容`,
        { dangerouslyUseHTMLString: true }
      ).then(() => {
        // 高亮当前阶段
        nextTick(() => {
          const timelineItems = document.querySelectorAll('.el-timeline-item')
          if (timelineItems[idx]) {
            timelineItems[idx].classList.add('error-highlight')
            setTimeout(() => {
              timelineItems[idx].classList.remove('error-highlight')
            }, 2000)
          }
        })
      })
    }
  }
}

// 调试函数
function debugForm() {
  console.log('=== 当前表单状态 ===')
  console.log('赛事编号:', form.code)
  console.log('赛事名称:', form.name)
  console.log('赛事类型:', form.type)
  console.log('赛事级别:', form.level)
  console.log('参赛形式:', form.participationType)
  console.log('主办方:', form.organizer)
  console.log('赛事简介:', form.description)
  console.log('赛事规则:', form.rules)
  console.log('阶段数量:', form.stages.length)
  console.log('阶段数据:', form.stages)
  console.log('表单引用数量:', stageFormRefs.value.length)
  console.log('表单引用:', stageFormRefs.value)
  console.log('提交时间警告状态:', submitTimeWarned.value)
  
  // 检查每个阶段的详细信息
  form.stages.forEach((stage, idx) => {
    console.log(`第${idx + 1}阶段详细信息:`, {
      type: stage.type,
      form: stage.form,
      signupStart: stage.signupStart,
      signupEnd: stage.signupEnd,
      submitStart: stage.submitStart,
      submitEnd: stage.submitEnd,
      leader: stage.leader,
      leaderPhone: stage.leaderPhone,
      remark: stage.remark,
      hasFormRef: !!stageFormRefs.value[idx]
    })
  })
  
  // 检查构建后的数据
  console.log('=== 构建后的提交数据 ===')
  const payload = buildCompetitionPayload(form)
  console.log('完整payload:', payload)
  console.log('参赛形式字段:', payload.participation_type)
  console.log('阶段数据:', payload.stages)
  if (payload.stages && payload.stages.length > 0) {
    console.log('第一个阶段的负责人电话:', payload.stages[0].leader_phone)
    console.log('第一个阶段的举办形式:', payload.stages[0].stage_form)
  }
  
  // 检查UUID生成情况
  console.log('=== UUID检查 ===')
  console.log('官方文件数量:', form.officialFiles ? form.officialFiles.length : 0)
  if (form.officialFiles && form.officialFiles.length > 0) {
    form.officialFiles.forEach((file, idx) => {
      console.log(`官方文件${idx + 1}:`, {
        name: file.name,
        url: file.url,
        uid: file.uid,
        uidLength: file.uid ? file.uid.length : 0,
        isValidUUID: file.uid ? /^[0-9a-f]{32}$/.test(file.uid) : false
      })
    })
  }
  
  console.log('宣传视频数量:', form.promotionalVideo ? form.promotionalVideo.length : 0)
  if (form.promotionalVideo && form.promotionalVideo.length > 0) {
    form.promotionalVideo.forEach((file, idx) => {
      console.log(`宣传视频${idx + 1}:`, {
        name: file.name,
        url: file.url,
        uid: file.uid,
        uidLength: file.uid ? file.uid.length : 0,
        isValidUUID: file.uid ? /^[0-9a-f]{32}$/.test(file.uid) : false
      })
    })
  }
  
  // 检查构建后的UUID
  console.log('=== 构建后的UUID ===')
  if (payload.official_files && payload.official_files.length > 0) {
    payload.official_files.forEach((file, idx) => {
      console.log(`构建后官方文件${idx + 1}:`, {
        file_name: file.file_name,
        file_url: file.file_url,
        uid: file.uid,
        uidLength: file.uid ? file.uid.length : 0,
        isValidUUID: file.uid ? /^[0-9a-f]{32}$/.test(file.uid) : false
      })
    })
  }
  
  if (payload.promotional_video_files && payload.promotional_video_files.length > 0) {
    payload.promotional_video_files.forEach((file, idx) => {
      console.log(`构建后宣传视频${idx + 1}:`, {
        file_name: file.file_name,
        file_url: file.file_url,
        uid: file.uid,
        uidLength: file.uid ? file.uid.length : 0,
        isValidUUID: file.uid ? /^[0-9a-f]{32}$/.test(file.uid) : false
      })
    })
  }
  
  console.log('============================================')
}
</script>

<style scoped>
.center-content {
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 24px;
}
.step-content {
  margin: 32px 0 24px 0;
}
.step-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
}
.upload-form-item .el-form-item__content {
  display: flex;
  justify-content: center;
}
.upload-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.upload-tip {
  color: #909399;
  font-size: 13px;
  margin-top: 16px;
  text-align: center;
  max-width: 400px;
  line-height: 1.6;
}

.preview-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.preview-card ul li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.preview-card ul li:last-child {
  border-bottom: none;
}

.preview-card ul li .el-icon {
  color: #409EFF;
  font-size: 16px;
}
.stage-card {
  background: #fafbff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.04);
  padding: 28px 36px 18px 36px;
  margin-bottom: 18px;
  border: none;
}
.stage-title {
  font-size: 16px;
  font-weight: normal;
  color: #333;
  margin-bottom: 8px;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
  display: flex;
  align-items: center;
}
.active-stage-title {
  font-size: 20px;
  font-weight: bold;
  color: #222;
}
.stage-card-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}
.stage-card .el-form-item__content > *:not(.el-date-editor) {
  max-width: 420px;
}
.stage-card .el-date-editor {
  min-width: 260px;
  max-width: 350px;
}
.el-timeline {
  padding-left: 42px !important;
}
.el-timeline-item__node {
  width: 18px !important;
  height: 18px !important;
  left: -9px !important;
  background: linear-gradient(135deg, #6366f1 60%, #a5b4fc 100%);
  box-shadow: 0 2px 8px rgba(99,102,241,0.18);
  border: 2px solid #fff;
  transition: box-shadow 0.2s, background 0.2s;
}
.is-active .el-timeline-item__node {
  background: linear-gradient(135deg, #4338ca 60%, #6366f1 100%);
  box-shadow: 0 4px 16px rgba(99,102,241,0.28);
  border: 2px solid #6366f1;
}
.el-timeline-item__tail {
  border-left: 3px solid #6366f1 !important;
  left: -1px !important;
}
.preview-cards {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.preview-card {
  margin-bottom: 0;
}
.preview-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 12px;
}
.stage-preview {
  margin-bottom: 18px;
}
.empty-tip {
  color: #bbb;
  text-align: center;
  margin: 16px 0;
}
.centered-dialog .el-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}
.float-btn {
  transition: transform 0.2s cubic-bezier(.4,2,.6,1), box-shadow 0.2s cubic-bezier(.4,2,.6,1);
}
.float-btn:hover {
  transform: translateY(-4px) scale(1.04);
  box-shadow: 0 8px 24px 0 rgba(99,102,241,0.18);
  z-index: 2;
}
.add-stage-dialog .el-dialog__body {
  padding-top: 12px;
  padding-bottom: 0;
}
.move-btn {
  transition: box-shadow 0.2s, background 0.2s, transform 0.2s;
  background: #f4f4f5;
  color: #6366f1;
  border: 1px solid #e5e7eb;
}
.move-btn:hover:not(:disabled) {
  box-shadow: 0 6px 18px rgba(99,102,241,0.18);
  background: #6366f1;
  color: #fff;
  border-color: #6366f1;
}
.stage-action-btn {
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 0.75rem;
  transition: box-shadow 0.2s, background 0.2s, transform 0.2s;
}

.stage-action-btn:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.05);
}

.stage-action-btn.delete-btn:hover:not(:disabled) {
  box-shadow: 0 2px 8px rgba(239,68,68,0.18);
  background: #dc2626;
  border-color: #dc2626;
}

.stage-action-btn.save-btn:hover:not(:disabled) {
  box-shadow: 0 2px 8px rgba(64,158,255,0.18);
  background: #409eff;
  border-color: #409eff;
}
.modern-card {
  width: 100%;
  max-width: 1100px;
  margin: 0 auto 32px auto;
  background: #fcfcfe;
  border-radius: 15px;
  border: 1.5px solid #f0f1f6;
  box-shadow: 0 10px 40px rgba(99,102,241,0.18);
  transition: box-shadow 0.3s, transform 0.3s;
  font-family: "Segoe UI", "PingFang SC", "Microsoft YaHei", sans-serif;
  position: relative;
  overflow: hidden;
  padding: 1.5rem 2rem 2rem 2rem;
  cursor: pointer;
}
.modern-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  z-index: 2;
}
.stage-badge-center {
  position: relative;
  top: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.35rem;
  font-weight: 700;
  color: #fff;
  background: linear-gradient(90deg, #6366f1 60%, #a5b4fc 100%);
  border-radius: 16px;
  padding: 0.36rem 1.6rem;
  letter-spacing: 1px;
  box-shadow: 0 4px 16px rgba(99,102,241,0.13);
  user-select: none;
  width: fit-content;
  margin-bottom: 1.6rem;
}
.stage-move-btns {
  position: absolute;
  top: 18px;
  left: 18px;
  z-index: 2;
  display: flex;
  align-items: center;
}

.time-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}
.error-highlight {
  box-shadow: 0 0 0 3px #f56c6c !important;
  border-radius: 8px;
  transition: box-shadow 0.3s;
}
</style>