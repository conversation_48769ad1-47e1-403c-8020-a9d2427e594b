<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElTable, ElTableColumn, ElDialog, ElTag, ElButton, ElCollapse, ElCollapseItem, ElDivider, ElTimeline, ElTimelineItem, ElIcon, ElTooltip, ElPagination, ElRadioGroup, ElRadio, ElInput } from 'element-plus';
import { DocumentCopy, Check, View, Clock, Search } from '@element-plus/icons-vue';
import { getAppealListApi, reviewAppealApi } from '../api/appeal';
import { useRouter } from 'vue-router';
import { userInfo } from '@/core/api/system.js';


const router = useRouter();
const tableData = ref([]);
const loading = ref(false);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const currentAppeal = ref(null);
const isViewingDetails = ref(false);
const hasReviewPermission = ref(false);


const searchPhone = ref('');


const filterStatus = ref('all');
const filterOptions = [
  { label: '全部', value: 'all' },
  { label: '待初审', value: 'pending_initial' },
  { label: '待复审', value: 'pending_second' },
  { label: '待终审', value: 'pending_final' },
  { label: '已通过', value: 'approved' },
  { label: '已驳回', value: 'rejected' },
];


const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);


const reviewStatusOptions = [
  { label: '初审通过', value: 'approved_initial', type: 'success' },
  { label: '初审驳回', value: 'rejected_initial', type: 'danger' },
  { label: '复审通过', value: 'approved_second', type: 'success' },
  { label: '复审驳回', value: 'rejected_second', type: 'danger' },
  { label: '终审通过', value: 'approved_final', type: 'success' },
  { label: '终审驳回', value: 'rejected_final', type: 'danger' },
];


const appealStatusMap = {
  'pending_initial': { label: '待初审', type: 'warning' },
  'pending_second': { label: '待复审', type: 'warning' },
  'pending_final': { label: '待终审', type: 'warning' },
  'PENDING': { label: '待初审', type: 'warning' }, // 兼容旧状态
  'approved': { label: '已通过', type: 'success' },
  'rejected': { label: '已驳回', type: 'danger' },
};


const reviewForm = reactive({
  status: '',
  reviewComment: '',
  reviewTime: '',
});


const rules = {
  status: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  reviewComment: [{ required: true, message: '请填写审核意见', trigger: 'blur' }],
  reviewTime: [{ required: true, message: '请填写审核时间', trigger: 'blur' }], // 审核时间也设为必填
};


const commentTemplates = [
  { label: '请选择模板', value: '' },
  { label: '模板1：信息完整，初审通过', value: '信息完整，符合初审要求，建议通过。' },
  { label: '模板2：证据不足，初审驳回', value: '证据材料不足以支撑申诉请求，初审驳回。' },
  { label: '模板3：情况属实，复审通过', value: '经复核，申诉人反映情况属实，建议复审通过。' },
  { label: '模板4：流程合规，复审驳回', value: '评审流程符合规定，复审驳回申诉。' },
  { label: '模板5：最终判定，终审通过', value: '根据前两轮审核及相关材料，最终判定申诉成立，终审通过。' },
  { label: '模板6：维持原判，终审驳回', value: '经最终审核，维持原评审结果，终审驳回申诉。' },
];


const selectedCommentTemplate = ref('');
const applyCommentTemplate = (templateValue) => {
  if (templateValue) {
    reviewForm.reviewComment = templateValue;
  }
};


const checkUserPermission = async () => {
  try {

    const response = await userInfo();
    if (response.code === 200) {
      const userData = response.data;
      const userRoles = userData.roles || [];

      hasReviewPermission.value = userData.admin === true || userRoles.includes('admin');

      if (!hasReviewPermission.value) {
        ElMessage.error('无权限进行申诉核查，仅管理员可操作');
        router.push('/');
        return false;
      }
      return true;
    } else {
      ElMessage.error('获取用户信息失败');
      return false;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    ElMessage.error('获取用户信息失败');
    return false;
  }
};

const allAppealData = ref([]);
const fetchAppealList = async () => {
  // 检查权限
  if (!(await checkUserPermission())) {
    return;
  }

  loading.value = true;
  try {
    const response = await getAppealListApi();
    if (response.code === 200) {
      allAppealData.value = response.data;
      total.value = allAppealData.value.length;
      // tableData 将通过 computed 属性获取过滤和分页后的数据，这里不再直接赋值
    } else {
      ElMessage.error(response.msg || '获取申诉列表失败');
    }
  } catch (error) {
    console.error('获取申诉列表失败:', error);
    if (error.response && error.response.status === 403) {
      ElMessage.error('无权限进行申诉核查，仅管理员可操作');
      router.push('/');
    } else {
      ElMessage.error('获取申诉列表失败');
    }
  } finally {
    loading.value = false;
  }
};


const filteredData = computed(() => {
  let data = allAppealData.value;

  // 添加调试信息
  console.log('所有申诉数据:', data);
  console.log('当前筛选状态:', filterStatus.value);
  console.log('申诉状态分布:', data.map(appeal => appeal.status));

  if (filterStatus.value !== 'all') {
      if (filterStatus.value === 'pending_initial') {
          // 兼容多种初审状态
          data = data.filter(appeal =>
            appeal.status === 'pending_initial' ||
            appeal.status === 'PENDING' ||
            appeal.status === 'pending'
          );
      } else if (filterStatus.value === 'pending_second') {
          data = data.filter(appeal => appeal.status === 'pending_second');
      } else if (filterStatus.value === 'pending_final') {
          data = data.filter(appeal => appeal.status === 'pending_final');
      } else if (filterStatus.value === 'approved') {
          data = data.filter(appeal => appeal.status === 'approved');
      } else if (filterStatus.value === 'rejected') {
          data = data.filter(appeal => appeal.status === 'rejected');
      } else {
          data = data.filter(appeal => appeal.status === filterStatus.value);
      }
  }

  console.log('筛选后数据:', data);

  if (searchPhone.value) {
      const searchTerm = searchPhone.value.trim();
      console.log('搜索关键词:', searchTerm);
      console.log('搜索前数据量:', data.length);
      console.log('数据中的电话号码:', data.map(appeal => appeal.submitterPhone));

      data = data.filter(appeal => {
        const phone = appeal.submitterPhone;
        if (!phone) {
          console.log('电话号码为空，跳过');
          return false;
        }

        // 支持多种搜索方式
        const phoneStr = phone.toString();
        const matches = phoneStr.includes(searchTerm) ||
                       phoneStr.toLowerCase().includes(searchTerm.toLowerCase());

        console.log(`电话 ${phone} 是否匹配 ${searchTerm}:`, matches);
        return matches;
      });

      console.log('搜索后数据量:', data.length);
  }

  return data;
});


const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredData.value.slice(start, end);
});


const handleFilterOrSearchChange = () => {
    currentPage.value = 1;
    total.value = filteredData.value.length;
};

// 监听筛选状态和搜索条件的改变
watch([filterStatus, searchPhone], handleFilterOrSearchChange);


const handleFilterChange = () => {
    handleFilterOrSearchChange();
};


const handleSearchInput = () => {
    handleFilterOrSearchChange();
};


const handlePageChange = (newPage) => {
    currentPage.value = newPage;
};

const handleSizeChange = (newSize) => {
    pageSize.value = newSize;
    currentPage.value = 1;
};


const openAppealDialog = (row) => {
  currentAppeal.value = row;
  reviewForm.status = '';
  reviewForm.reviewComment = '';
  reviewForm.reviewTime = '';
  selectedCommentTemplate.value = '';

  // 如果是审核模式，自动设置当前时间
  if (row.status !== 'approved' && row.status !== 'rejected') {
      setCurrentReviewTime();
      isViewingDetails.value = false;
  } else {
      isViewingDetails.value = true;
  }

  dialogVisible.value = true;
};


const submitReview = async () => {
  if (!currentAppeal.value) return;

  // 检查权限
  if (!hasReviewPermission.value) {
    ElMessage.error('无权限进行申诉核查，仅管理员可操作');
    return;
  }

  submitLoading.value = true;
  try {
    const currentStage = getCurrentReviewStage(currentAppeal.value);

    const allowedOptions = reviewStatusOptions.filter(opt =>
       (currentStage === 'initial' && (opt.value === 'approved_initial' || opt.value === 'rejected_initial')) ||
       (currentStage === 'second' && (opt.value === 'approved_second' || opt.value === 'rejected_second')) ||
       (currentStage === 'final' && (opt.value === 'approved_final' || opt.value === 'rejected_final'))
    );

    const selectedOption = allowedOptions.find(opt => opt.value === reviewForm.status);

    if (!selectedOption) {
         ElMessage.warning('请选择正确的审核结果');
         submitLoading.value = false;
         return;
     }

     // 调试信息
     console.log('审核时间值:', reviewForm.reviewTime);
     console.log('审核时间类型:', typeof reviewForm.reviewTime);
     console.log('审核时间长度:', reviewForm.reviewTime ? reviewForm.reviewTime.length : 0);

     if (!reviewForm.reviewTime || reviewForm.reviewTime.trim() === '') {
          ElMessage.warning('请填写审核时间');
          submitLoading.value = false;
          return;
     }

    // 使用新的VO格式提交数据
    const reviewData = {
      appealId: currentAppeal.value.id,
      status: reviewForm.status,
      reviewComment: reviewForm.reviewComment,
      reviewTime: reviewForm.reviewTime,
    };

    console.log('提交的审核数据:', reviewData);
    const response = await reviewAppealApi(reviewData);
    console.log('后端响应:', response);

    if (response && response.code === 200) {
      ElMessage.success(
        reviewForm.status && reviewForm.status.startsWith('approved')
          ? '审核成功，状态已自动流转到下阶段'
          : '申诉已终止，状态为已驳回'
      );
      dialogVisible.value = false;
      fetchAppealList();
    } else {
      console.error('审核提交失败，响应:', response);
      ElMessage.error(response.msg || '审核提交失败');
    }
  } catch (error) {
    console.error('审核提交失败:', error);
    if (error.response && error.response.status === 403) {
      ElMessage.error('无权限进行申诉核查，仅管理员可操作');
    } else {
      ElMessage.error('提交失败，请稍后重试');
    }
  } finally {
    submitLoading.value = false;
  }
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  // 尝试解析为Date对象，处理可能的ISO 8601字符串
  const dateObj = new Date(date);
   if (isNaN(dateObj.getTime())) return date;
  return dateObj.toLocaleString();
};

// 获取状态标签类型（根据主状态）
const getStatusType = (status) => {
  const statusInfo = appealStatusMap[status];
  if (statusInfo) return statusInfo.type;

  // 对于历史记录中的阶段状态，判断通过或驳回
  if (status && status.includes('approved')) return 'success';
  if (status && status.includes('rejected')) return 'danger';

  return 'info'; // 默认类型
};

// 获取状态标签文本（根据主状态）
const getStatusLabel = (status) => {
  const statusInfo = appealStatusMap[status];
  if (statusInfo) return statusInfo.label;

   // 对于历史记录中的阶段状态，判断通过或驳回
  if (status === 'approved_initial') return '初审通过';
  if (status === 'rejected_initial') return '初审驳回';
  if (status === 'approved_second') return '复审通过';
  if (status === 'rejected_second') return '复审驳回';
  if (status === 'approved_final') return '终审通过';
  if (status === 'rejected_final') return '终审驳回';

  return status; // 默认返回原始状态
};

// 获取当前申诉的审核阶段（用于控制按钮和对话框内容）
const getCurrentReviewStage = (appeal) => {
    if (!appeal) return '';
    if (appeal.status === 'approved' || appeal.status === 'rejected') {
        return 'completed'; // 已完成
    }
    if (appeal.status === 'pending_initial' || appeal.status === 'PENDING') {
        return 'initial'; // 待初审（兼容PENDING状态）
    }
     if (appeal.status === 'pending_second') {
        return 'second'; // 待复审
    }
     if (appeal.status === 'pending_final') {
        return 'final'; // 待终审
    }
    return ''; // 未知状态
};

// 获取操作按钮文本
const getOperationButtonText = (status) => {
    const stage = getCurrentReviewStage({ status }); // 传递一个模拟对象以复用逻辑
    switch(stage) {
        case 'initial': return '初审';
        case 'second': return '复审';
        case 'final': return '终审';
        case 'completed': return '查看详情';
        default: return '操作';
    }
};

// 获取审核阶段的中文名称
const getReviewStageLabel = (stage) => {
    switch(stage) {
        case 'initial': return '初审';
        case 'second': return '复审';
        case 'final': return '终审';
        default: return stage; // 如果是完整的状态值（如approved_initial），直接返回
    }
};

// 复制到剪贴板
const copyToClipboard = (text, message) => {
  if (!navigator.clipboard) {
    ElMessage.error('浏览器不支持复制到剪贴板功能');
    return;
  }
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success(message || '复制成功');
  }).catch(err => {
    console.error('复制失败:', err);
    ElMessage.error('复制失败');
  });
};

// 根据当前申诉状态过滤审核结果选项
const filteredReviewStatusOptions = computed(() => {
    const currentStage = getCurrentReviewStage(currentAppeal.value);
    if (!currentStage || currentStage === 'completed') return [];

     return reviewStatusOptions.filter(opt =>
       (currentStage === 'initial' && (opt.value === 'approved_initial' || opt.value === 'rejected_initial')) ||
       (currentStage === 'second' && (opt.value === 'approved_second' || opt.value === 'rejected_second')) ||
       (currentStage === 'final' && (opt.value === 'approved_final' || opt.value === 'rejected_final'))
    );
});

// 设置当前时间到审核时间字段
const setCurrentReviewTime = () => {
  const now = new Date();
  // 格式化为 YYYY-MM-DD HH:mm:ss 格式
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  reviewForm.reviewTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

onMounted(async () => {
  // 首先检查权限，然后获取申诉列表
  if (await checkUserPermission()) {
    fetchAppealList();
  }
});
</script>

<template>
  <div class="page">
    <div class="review-card">
      <h1>申诉审核管理</h1>

      <!-- 搜索和筛选控件 -->
      <div class="controls-container" style="display: flex; align-items: center; margin-bottom: 16px;">
          <!-- 搜索输入框和按钮 -->
          <div class="search-area" style="display: flex; align-items: center;">
              <el-input
                  v-model="searchPhone"
                  placeholder="按提交人电话搜索"
                  :prefix-icon="Search"
                  @input="handleSearchInput"
                  @keyup.enter="handleSearchInput"
                  style="width: 200px; margin-right: 10px;"
              />
               <el-button type="primary" :icon="Search" @click="handleSearchInput">搜索</el-button>
          </div>

          <!-- 筛选器 -->
          <div class="filter-controls">
              <label style="margin-right: 10px;">筛选状态:</label>
              <el-radio-group v-model="filterStatus" @change="handleFilterChange">
                  <el-radio-button
                      v-for="option in filterOptions"
                      :key="option.value"
                      :label="option.value"
                  >{{ option.label }}</el-radio-button>
              </el-radio-group>
          </div>
      </div>

      <!-- 申诉列表表格 -->
      <el-table
          :data="paginatedData"
          v-loading="loading"
          style="width: 100%"
          border
          empty-text="暂无申诉数据～"
      >
        <el-table-column prop="id" label="申诉ID" width="80" />
        <el-table-column prop="applicationType" label="申诉类型" width="120">
          <template #default="{ row }">
            {{ row.applicationType === 'player_complaint' ? '选手投诉' :
              row.applicationType === 'player_appeal' ? '选手申诉' :
                  row.applicationType === 'team_complaint' ? '参赛队投诉' :
                      row.applicationType === 'team_appeal' ? '参赛队申诉' :
                          row.applicationType === 'process_appeal' ? '流程申诉' : '其他申诉' }}
          </template>
        </el-table-column>
        <el-table-column prop="submitter" label="提交人" width="120" />
        <el-table-column prop="submitterUnit" label="提交单位" width="150" />
        <el-table-column prop="request" label="诉求" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="提交时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
                v-if="hasReviewPermission"
                type="primary"
                size="small"
                :icon="getCurrentReviewStage(row) === 'completed' ? View : Check"
                @click="openAppealDialog(row)"
            >
              {{ getOperationButtonText(row.status) }}
            </el-button>
            <span v-else style="color: #999; font-size: 12px;">无权限</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          background
          style="margin-top: 20px; text-align: right;"
      />

      <!-- 申诉详情/审核对话框 -->
      <el-dialog
          v-model="dialogVisible"
          :title="isViewingDetails ? '申诉详情' : '申诉审核 (' + getStatusLabel(currentAppeal?.status) + ')'"
          width="60%"
          :close-on-click-modal="false"
      >
        <div v-if="currentAppeal" class="review-dialog-content">

           <!-- 申诉详情部分 (始终显示) -->
           <div class="appeal-info">
            <h3>申诉详情</h3>
            <div class="info-item">
              <label>申诉类型：</label>
              <span>{{ currentAppeal.applicationType === 'player_complaint' ? '选手投诉' :
               currentAppeal.applicationType === 'player_appeal' ? '选手申诉' :
               currentAppeal.applicationType === 'team_complaint' ? '参赛队投诉' :
               currentAppeal.applicationType === 'team_appeal' ? '参赛队申诉' :
               currentAppeal.applicationType === 'process_appeal' ? '流程申诉' : '其他申诉' }}</span>
            </div>
             <!-- 其他申诉详情字段 -->
            <div class="info-item"><label>作品编号:</label><span>{{ currentAppeal.workId }}</span></div>
            <div class="info-item"><label>作品参赛单位:</label><span>{{ currentAppeal.submittingUnit }}</span></div>
            <div class="info-item"><label>提交人:</label><span>{{ currentAppeal.submitter }}</span></div>
            <div class="info-item"><label>提交人单位:</label><span>{{ currentAppeal.submitterUnit }}</span></div>
            <div class="info-item"><label>提交人电话:</label><span>{{ currentAppeal.submitterPhone }}</span></div>
            <div class="info-item"><label>提交人信箱:</label><span>{{ currentAppeal.submitterEmail }}</span></div>

            <div class="info-item full-width">
              <label>诉求：</label>
              <span>{{ currentAppeal.request }}</span>
            </div>
            <div class="info-item full-width">
              <label>详细说明：</label>
              <span>{{ currentAppeal.explanation }}</span>
            </div>
            <div class="info-item">
              <label>证据链接：</label>
              <span>{{ currentAppeal.evidenceLink }}</span>
              <el-button type="text" :icon="DocumentCopy" @click="copyToClipboard(currentAppeal.evidenceLink, '证据链接已复制')" size="small" />
            </div>
            <div class="info-item">
              <label>提取码：</label>
              <span>{{ currentAppeal.extractionCode }}</span>
               <el-button type="text" :icon="DocumentCopy" @click="copyToClipboard(currentAppeal.extractionCode, '提取码已复制')" size="small" />
            </div>
             <div class="info-item"><label>提交时间:</label><span>{{ formatDate(currentAppeal.createTime) }}</span></div>
             <div class="info-item"><label>当前状态:</label><span><el-tag :type="getStatusType(currentAppeal.status)">{{ getStatusLabel(currentAppeal.status) }}</el-tag></span></div>
          </div>

           <!-- 审核历史部分 (只读 - 时间轴形式) -->
           <div v-if="currentAppeal.reviews && currentAppeal.reviews.length > 0" class="review-history-section">
              <h3>审核历史</h3>
              <el-timeline>
                 <el-timeline-item
                    v-for="(review, index) in currentAppeal.reviews"
                    :key="index"
                    :timestamp="formatDate(review.reviewTime)"
                    placement="top"
                    :type="getStatusType(review.status)"
                    >
                    <h4>{{ getReviewStageLabel(review.stage) }}</h4>
                    <p><strong>结果:</strong> <el-tag :type="getStatusType(review.status)">{{ getStatusLabel(review.status) }}</el-tag></p>
                    <p><strong>意见:</strong> {{ review.comment || '无' }}</p>
                 </el-timeline-item>
              </el-timeline>
           </div>

          <!-- 当前阶段审核表单 (仅在待审核状态显示) -->
          <div v-if="!isViewingDetails" class="review-form">
            <h3>当前审核阶段: {{ getStatusLabel(currentAppeal?.status) }}</h3>
            <el-form :model="reviewForm" :rules="rules" label-width="100px">
              <el-form-item label="审核结果" prop="status">
                <el-select v-model="reviewForm.status" placeholder="请选择审核结果" style="width: 100%;">
                  <el-option
                      v-for="option in filteredReviewStatusOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </el-form-item>
               <el-form-item label="意见模板">
                 <el-select
                     v-model="selectedCommentTemplate"
                     placeholder="选择意见模板"
                     @change="applyCommentTemplate"
                     style="width: 100%;"
                 >
                   <el-option
                       v-for="template in commentTemplates"
                       :key="template.value"
                       :label="template.label"
                       :value="template.value"
                   />
                 </el-select>
               </el-form-item>
                 <el-form-item label="审核时间" prop="reviewTime">
                     <div style="display: flex; width: 100%; align-items: center;">
                        <el-input v-model="reviewForm.reviewTime" placeholder="请填写审核时间" style="flex-grow: 1; margin-right: 10px;"></el-input>
                         <el-tooltip content="插入当前时间" placement="top">
                           <el-button :icon="Clock" @click="setCurrentReviewTime"></el-button>
                         </el-tooltip>
                     </div>
                 </el-form-item>
              <el-form-item label="审核意见" prop="reviewComment">
                <el-input
                    type="textarea"
                    v-model="reviewForm.reviewComment"
                    :rows="4"
                    placeholder="请输入审核意见"
                />
              </el-form-item>
            </el-form>
          </div>

        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">{{ isViewingDetails ? '关闭' : '取消' }}</el-button>
            <el-button
                v-if="!isViewingDetails && hasReviewPermission"
                type="primary"
                @click="submitReview"
                :loading="submitLoading"
                :disabled="submitLoading"
            >
              提交审核
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<style scoped>
.page {
  padding: 20px;
  background: #fff;
  min-height: 100vh;
}

.review-card {
  background: transparent;
  border-radius: 4px;
  box-shadow: none;
  padding: 24px 20px 12px 20px;
  width: 100%;
  margin: 0;
  min-height: calc(100vh - 40px);
}

h1 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  letter-spacing: 1px;
  text-align: left;
}

h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px;
  color: #333;
}

.review-dialog-content {
  padding: 0 20px 20px;
}

.appeal-info,
.review-history-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
  flex-shrink: 0;
  min-width: 80px;
}

.info-item span {
  color: #333;
   word-break: break-all;
   flex-grow: 1;
}

.info-item .el-button {
    margin-left: 8px;
    padding: 0 5px;
    height: auto;
}

.form-item.full-width .info-item label,
.form-item.full-width .info-item span {
    min-width: auto;
}

.review-form {
  margin-top: 24px;
  padding: 16px;
   background: #f8f9fa;
  border-radius: 4px;
}

.review-form h3 {
    margin-top: 0;
    margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-table) {
  margin-top: 16px;
}

:deep(.el-tag) {
  text-align: center;
  min-width: 60px;
}

.review-history-section h3 {
     margin-top: 0;
     margin-bottom: 15px;
}

.review-history-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
}

:deep(.el-timeline-item__node) {
    z-index: 1;
}

:deep(.el-timeline-item__wrapper) {
    padding-left: 15px;
}

:deep(.el-timeline-item__content) {
    font-size: 14px;
    color: #555;
}

:deep(.el-timeline-item__timestamp) {
    font-size: 13px;
    color: #909399;
}

.review-history-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.review-history-item h4 {
    margin: 0 0 10px;
    font-size: 14px;
    color: #555;
}

.review-history-item p {
    margin-bottom: 8px;
    line-height: 1.5;
    color: #555;
}

.review-history-item p strong {
    color: #333;
}

.review-completed-message {
    margin-top: 20px;
    padding: 20px;
    text-align: center;
}

.info-item span .el-tag {
    vertical-align: middle;
}

.controls-container {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.search-area {
    display: flex;
    align-items: center;
}

.filter-controls {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    margin-left: 20px;
}

</style>