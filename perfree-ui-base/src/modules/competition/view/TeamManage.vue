zzfr<template>
  <div class="container mx-auto px-4 py-6 flex">
    <!-- 渐进式团队切换：左侧团队切换区隐藏，Drawer 控制团队切换 -->
    <el-drawer v-model="showSidebar" title="切换团队" direction="ltr" size="280px" class="team-drawer">
      <div class="drawer-header mb-4">
        <div class="drawer-header-inner">
          <div class="font-bold text-lg mb-1">团队切换</div>
          <div class="text-gray-500 text-xs">选择你要管理的团队</div>
        </div>
      </div>
      <div class="team-switch-card-list">
        <div
            v-for="team in filteredTeams"
            :key="team.id"
            class="team-switch-card"
            :class="{ active: isActive(team) }"
            @click="selectTeam(team); showSidebar = false"
        >
          <el-avatar :size="32" class="mr-2" :src="team.avatar || `https://api.dicebear.com/7.x/identicon/svg?seed=${team.name}`">
            {{ team.name.charAt(0) }}
          </el-avatar>
          <div class="flex-1 min-w-0">
            <div class="team-card-title">{{ team.name }}</div>
            <div class="team-card-info">编号：{{ team.code }}</div>
            <div class="team-card-info">队长：{{ team.leader }}</div>
          </div>
        </div>
      </div>
      <div class="drawer-footer mt-6 flex justify-center">
        <el-button type="primary" class="drawer-create-btn" @click="openAddTeamDialog">+ 创建新团队</el-button>
      </div>
    </el-drawer>

    <!-- 右侧主内容区 -->
    <ModernCard class="main-content flex-1 ml-6" style="position:relative;">
      <!-- 大标题和页面描述 -->
      <div class="page-title-row">
        <div class="page-title">
          <h1>我的团队</h1>
          <div class="page-desc">作为队长，您可以管理团队成员、分配角色、处理申请、查看数据等</div>
        </div>
        <div class="main-header">
          <el-button type="primary" class="switch-team-btn" @click="showSidebar = true">
            切换团队
          </el-button>
          <el-button type="success" class="mail-btn" @click="openMailCard">
            邮箱通知
          </el-button>
        </div>
      </div>
      <!-- 邮箱悬浮卡片 -->
      <el-card
          v-if="showMailCard"
          class="mail-float-card"
      >
        <div class="mail-card-header">
          <span>邮箱通知</span>
          <el-icon class="close-icon" @click="showMailCard = false"><Close /></el-icon>
        </div>
        <el-tabs v-model="mailTab">
          <el-tab-pane label="组队通知" name="team">
            <div class="mail-table-header">
              <span style="width: 40px; display:inline-block;"></span>
              <span class="mail-col-content">内容</span>
              <span class="mail-col-time">时间</span>
              <span class="mail-col-action">操作</span>
              <el-button size="small" type="primary" @click="selectAllTeamNotices" style="margin-left:10px;">全选</el-button>
              <el-button v-if="hasSelection && mailTab==='team'" size="small" type="danger" @click="deleteSelectedMessages" style="margin-left:10px;">删除</el-button>
            </div>
            <div class="mail-table-body">
              <div v-for="(row, idx) in teamNotices" :key="idx" class="mail-table-row">
                <el-checkbox v-model="row._checked" @change="onRowCheckChange('team')" style="margin-right:8px;" />
                <span class="mail-col-content">{{ row.content }}</span>
                <span class="mail-col-time">{{ row.time }}</span>
                <span class="mail-col-action">
                  <template v-if="row.status === '待处理'">
                    <el-button size="small" type="success" @click="handleNoticeAgree(row)">同意</el-button>
                    <el-button size="small" type="danger" @click="handleNoticeReject(row)">拒绝</el-button>
                  </template>
                  <template v-else>
                    <span>{{ row.status }}</span>
                  </template>
                </span>
              </div>
              <div v-if="!teamNotices.length" class="empty-msg">暂无组队通知</div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="系统消息" name="system">
            <div class="mail-table-header">
              <span style="width: 40px; display:inline-block;"></span>
              <span class="mail-col-content">内容</span>
              <span class="mail-col-time">时间</span>
              <span class="mail-col-action">操作</span>
              <el-button size="small" type="primary" @click="selectAllSystemMessages" style="margin-left:10px;">全选</el-button>
              <el-button v-if="hasSelection && mailTab==='system'" size="small" type="danger" @click="deleteSelectedMessages" style="margin-left:10px;">删除</el-button>
            </div>
            <div class="mail-table-body">
              <div v-for="(row, idx) in systemMessages" :key="idx" class="mail-table-row">
                <el-checkbox v-model="row._checked" @change="onRowCheckChange('system')" style="margin-right:8px;" />
                <span class="mail-col-content">{{ row.content }}</span>
                <span class="mail-col-time">{{ row.time }}</span>
                <span class="mail-col-action">
                  <span>{{ row.status }}</span>
                </span>
              </div>
              <div v-if="!systemMessages.length" class="empty-msg">暂无系统消息</div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
      <!-- 欢迎页/空态引导 -->
      <div v-if="teams.length === 0" class="welcome-empty flex flex-col items-center justify-center py-16">
        <img src="https://cdn.jsdelivr.net/gh/element-plus/element-plus@dev/docs/public/empty-image.svg" alt="欢迎" style="width:180px;margin-bottom:24px;">
        <h2 class="text-2xl font-bold mb-2">欢迎使用团队管理</h2>
        <p class="mb-4 text-gray-500">你还没有团队，快来创建你的第一个团队吧！</p>
        <el-button type="primary" @click="openAddTeamDialog">创建团队</el-button>
      </div>
      <!-- 搜索与筛选区域 -->
      <div v-if="!selectedTeam" class="global-actions mb-6">
        <el-input
            v-model="searchQuery"
            placeholder="搜索团队名称"
            class="search-input"
            clearable
        />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>

      <!-- 团队列表视图 -->
      <div v-if="!selectedTeam && teams.length > 0" class="team-list">
        <el-card
            v-for="team in filteredTeams"
            :key="team.id"
            shadow="never"
            class="mb-4 team-card"
            @click="selectTeam(team)"
            :class="{ 'team-card-active': isActive(team) }"
        >
          <div class="flex items-center justify-between">
            <!-- 团队头像点缀 -->
            <el-avatar :size="48" class="mr-4" :src="team.avatar || `https://api.dicebear.com/7.x/identicon/svg?seed=${team.name}`">
              {{ team.name.charAt(0) }}
            </el-avatar>
            <div class="flex-1 min-w-0">
              <h3 class="font-bold text-lg mb-1 truncate">{{ team.name }}</h3>
              <p class="text-gray-500 text-sm truncate">编号：{{ team.code }} | 队长：{{ team.leader }}</p>
              <div class="flex flex-wrap gap-2 mt-2 text-sm">
                <span>队员数：{{ team.members.length }}</span>
                <span>赛事：{{ team.competition }}</span>
              </div>
              <div class="text-gray-500 text-xs mt-1 truncate">备注：{{ team.remark || '无' }}</div>
            </div>
            <el-tag :type="formatStatus(team.status)" size="small">
              {{ team.status }}
            </el-tag>
          </div>
        </el-card>
      </div>

      <!-- 团队详情视图 -->
      <div v-else class="team-detail">
        <!-- 团队全流程进度条 -->
        <el-steps :active="currentStep" finish-status="success" align-center class="team-steps-optimized">
          <el-step title="组建团队" @click="showStepDetail(0)" />
          <el-step title="审核中" @click="showStepDetail(1)" />
          <el-step title="报名赛事" @click="showStepDetail(2)" />
          <el-step title="参赛中" @click="showStepDetail(3)" />
          <el-step title="获奖/结束" @click="showStepDetail(4)" />
        </el-steps>
        <div class="divider divider-after-steps"></div>
        <!-- 团队信息头部 -->
        <div class="team-header team-header-optimized flex justify-between items-center mb-6 pb-4 border-b">
          <div class="flex items-center">
            <el-avatar :src="selectedTeam.logo" :size="50" class="mr-4" />
            <div>
              <h2 class="section-title mb-1">{{ selectedTeam.name }}</h2>
              <p class="text-gray-500">{{ selectedTeam.description || '暂无描述' }}</p>
            </div>
          </div>
          <div class="action-group team-header-actions">
            <el-button type="primary" @click="openAddMemberDialog">添加队员</el-button>
            <el-button @click="openEditTeamDialog">编辑团队</el-button>
            <el-button type="danger" @click="openDeleteTeamDialog">删除团队</el-button>
          </div>
        </div>
        <div class="divider"></div>
        <!-- 团队成员结构与二维码并排区块 -->
        <div class="team-visualization-row">
          <!-- 左侧：团队成员结构 -->
          <div class="team-visualization">
            <el-card class="visual-card">
              <h3 class="section-title mb-4">团队成员结构</h3>
              <div class="chart-container" ref="chartRef"></div>
            </el-card>
          </div>
          <!-- 右侧：二维码与邀请码 -->
          <div class="team-qrcode-box">
            <el-card class="visual-card qrcode-card">
              <h3 class="section-title mb-4 qrcode-title">团队二维码</h3>
              <div class="qrcode-content-center">
                <div>
                  <div class="qrcode-img-wrap">
                    <img :src="qrcodeUrl" alt="团队二维码" class="qrcode-img" ref="qrcodeImgRef" />
                  </div>
                  <div class="qrcode-invite">邀请码：<span class="font-bold text-lg invite-code">{{ inviteCode }}</span></div>
                </div>
                <div class="qrcode-btns">
                  <el-button size="small" type="primary" @click="downloadQrcode">下载二维码</el-button>
                  <el-button size="small" type="success" @click="copyInviteCode">复制邀请码</el-button>
                </div>
              </div>
            </el-card>
          </div>
        </div>
        <div class="divider"></div>
        <!-- 团队成员列表分区（下移） -->
        <div class="member-management">
          <div class="member-list-header" style="display:flex;align-items:center;justify-content:space-between;margin-bottom:24px;">
            <h3 class="section-title mb-0">团队成员列表</h3>
            <el-dropdown v-if="selectedTeam" class="more-actions-dropdown">
              <el-button type="default" class="more-btn">更多操作</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="openAnnounceDialog">团队公告</el-dropdown-item>
                  <el-dropdown-item @click="logDialogVisible = true">操作日志</el-dropdown-item>
                  <el-dropdown-item @click="openTransferCaptainDialog">转让队长</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <el-row :gutter="20">
            <el-col v-for="member in selectedTeam.members" :key="member.id" :sm="24" :md="12" :lg="8">
              <el-card class="member-card modern-shadow mb-4">
                <div class="flex items-center mb-3">
                  <el-avatar :size="90" :src="getMemberAvatar(member)" class="avatar-modern" />
                  <div class="ml-5 flex-1">
                    <div class="font-bold text-xl mb-1">{{ member.name }}</div>
                    <el-tag :type="member.role === '队长' ? 'success' : member.role === '替补' ? 'warning' : 'info'" size="small" class="modern-tag">{{ member.role }}</el-tag>
                    <div class="text-gray-500 text-sm mt-1">学号：{{ member.studentId }}</div>
                    <div class="text-gray-500 text-sm">学校：{{ member.school }}</div>
                    <div class="text-gray-500 text-sm">年级：{{ member.grade }}</div>
                    <div class="text-gray-500 text-sm">专业：{{ member.major }}</div>
                    <div class="text-gray-500 text-sm">电话：{{ member.phone }}</div>
                    <div class="text-gray-500 text-sm">邮箱：{{ member.email }}</div>
                  </div>
                </div>
                <div class="text-gray-500 text-sm mb-1">简介：{{ member.bio || '无' }}</div>
                <div class="mt-2 flex space-x-2 action-group">
                  <el-button size="small" type="primary" @click="editMember(member)">编辑</el-button>
                  <el-button size="small" type="info" @click="viewMemberDetail(member)">详情</el-button>
                  <el-button size="small" type="danger" @click="deleteMember(member)">删除</el-button>
                  <el-button size="small" type="success" @click="viewMemberExperience(member)">查看经历</el-button>
                </div>
                <!-- 参赛经历列表 -->
                <div class="experience-list mt-4">
                  <div class="flex justify-between items-center mb-2">
                    <span class="font-bold">参赛经历</span>
                    <el-button size="small" type="primary" @click="openAddExperience(member)">添加经历</el-button>
                  </div>
                  <el-timeline class="exp-timeline">
                    <el-timeline-item
                        v-for="(exp, idx) in member.experiences || []"
                        :key="idx"
                        :timestamp="exp.time"
                        :type="exp.award ? 'success' : 'info'"
                        :hollow="true"
                    >
                      <div class="exp-item">
                        <div class="flex justify-between items-center">
                          <div>
                            <span class="font-bold">{{ exp.competitionName }}</span>
                            <el-tag size="small" class="ml-2 modern-tag">{{ exp.award }}</el-tag>
                          </div>
                          <div class="exp-action-group">
                            <el-button size="small" type="info" @click="openEditExperience(member, exp, idx)">编辑</el-button>
                            <el-button size="small" type="danger" @click="deleteExperience(member, idx)">删除</el-button>
                          </div>
                        </div>
                        <div class="text-gray-500 text-sm">角色：{{ exp.role }}</div>
                        <div class="text-gray-500 text-sm">描述：{{ exp.description }}</div>
                        <div v-if="exp.certificate" class="mt-1">
                          <el-link :href="exp.certificate" target="_blank">查看奖状</el-link>
                        </div>
                      </div>
                    </el-timeline-item>
                    <el-timeline-item v-if="!(member.experiences && member.experiences.length)" type="info" hollow>
                      <span class="text-gray-400">暂无经历</span>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </ModernCard>

    <!-- 添加/编辑团队对话框 -->
    <el-dialog
        v-model="teamDialogVisible"
        :title="teamForm.id ? '编辑团队' : '创建团队'"
        width="500px"
    >
      <el-form
          ref="teamFormRef"
          :model="teamForm"
          :rules="teamRules"
          label-width="100px"
      >
        <el-form-item label="团队名称" prop="name">
          <el-input v-model="teamForm.name" />
        </el-form-item>
        <el-form-item label="团队描述" prop="description">
          <el-input
              v-model="teamForm.description"
              type="textarea"
              :rows="3"
          />
        </el-form-item>
        <el-form-item label="团队标签" prop="tags">
          <el-select
              v-model="teamForm.tags"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请选择或输入标签"
          >
            <el-option
                v-for="tag in defaultTags"
                :key="tag"
                :label="tag"
                :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="teamDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitTeamForm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 添加/编辑成员对话框 -->
    <el-dialog
        v-model="memberDialogVisible"
        :title="memberForm.id ? '编辑成员' : '添加成员'"
        width="500px"
    >
      <el-form
          ref="memberFormRef"
          :model="memberForm"
          :rules="memberRules"
          label-width="100px"
      >
        <el-form-item label="头像">
          <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :on-change="handleMemberAvatarChange"
          >
            <el-avatar
                v-if="memberForm.avatar"
                :src="memberForm.avatar"
                :size="80"
                class="cursor-pointer"
            />
            <el-button v-else type="primary">上传头像</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="memberForm.name" />
        </el-form-item>
        <el-form-item label="学号" prop="studentId">
          <el-input v-model="memberForm.studentId" />
        </el-form-item>
        <el-form-item label="学校" prop="school">
          <el-input v-model="memberForm.school" />
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-input v-model="memberForm.grade" />
        </el-form-item>
        <el-form-item label="专业" prop="major">
          <el-input v-model="memberForm.major" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="memberForm.phone" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="memberForm.email" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="memberForm.role">
            <el-option
                v-for="role in memberRoles"
                :key="role.value"
                :label="role.label"
                :value="role.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="个人简介" prop="bio">
          <el-input
              v-model="memberForm.bio"
              type="textarea"
              :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="memberDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMember">确定</el-button>
      </template>
    </el-dialog>

    <!-- 成员详情对话框 -->
    <el-dialog
        v-model="memberDetailVisible"
        title="成员详情"
        width="500px"
    >
      <div class="member-detail">
        <div class="text-center mb-6">
          <el-avatar
              :src="detailMember.avatar || getMemberAvatar(detailMember)"
              :size="100"
          >
            {{ detailMember.name?.charAt(0) }}
          </el-avatar>
          <h3 class="text-xl font-bold mt-3">{{ detailMember.name }}</h3>
          <el-tag :type="detailMember.role === '队长' ? 'success' : 'info'" class="mt-2">
            {{ detailMember.role }}
          </el-tag>
        </div>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="学号">{{ detailMember.studentId }}</el-descriptions-item>
          <el-descriptions-item label="学校">{{ detailMember.school }}</el-descriptions-item>
          <el-descriptions-item label="年级">{{ detailMember.grade }}</el-descriptions-item>
          <el-descriptions-item label="专业">{{ detailMember.major }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ detailMember.email }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ detailMember.phone }}</el-descriptions-item>
          <el-descriptions-item label="加入时间">{{ formatDate(detailMember.joinedAt) }}</el-descriptions-item>
          <el-descriptions-item label="个人简介">{{ detailMember.bio || '暂无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
        v-model="deleteDialogVisible"
        :title="isDeletingTeam ? '删除团队' : '删除成员'"
        width="400px"
    >
      <div class="text-center">
        <el-icon class="text-danger mb-4" :size="48"><Warning /></el-icon>
        <p class="mb-4">
          确定要删除
          <template v-if="isDeletingTeam">
            团队 <span class="text-primary font-bold">{{ selectedTeam?.name }}</span>
          </template>
          <template v-else>
            成员 <span class="text-primary font-bold">{{ memberForm.name }}</span>
          </template>
          吗？
        </p>
        <p class="text-danger text-sm" v-if="isDeletingTeam">
          此操作将永久删除该团队及其所有相关数据，请谨慎操作！
        </p>
      </div>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确定删除</el-button>
      </template>
    </el-dialog>

    <!-- 队员经历编辑对话框 -->
    <el-dialog
        v-model="experienceDialogVisible"
        title="添加/编辑经历"
        width="500px"
    >
      <el-form
          ref="experienceFormRef"
          :model="experienceForm"
          :rules="experienceRules"
          label-width="100px"
      >
        <el-form-item label="比赛名称" prop="competitionName">
          <el-input v-model="experienceForm.competitionName" />
        </el-form-item>
        <el-form-item label="时间" prop="time">
          <el-date-picker
              v-model="experienceForm.time"
              type="date"
              placeholder="选择时间"
          />
        </el-form-item>
        <el-form-item label="奖项" prop="award">
          <el-input v-model="experienceForm.award" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-input v-model="experienceForm.role" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
              v-model="experienceForm.description"
              type="textarea"
              :rows="3"
          />
        </el-form-item>
        <el-form-item label="奖状文件" prop="certificate">
          <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :on-change="handleCertificateUpload"
          >
            <el-avatar
                v-if="experienceForm.certificate"
                :src="experienceForm.certificate"
                :size="80"
                class="cursor-pointer"
            />
            <el-button v-else type="primary">上传奖状文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="experienceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveExperience">确定</el-button>
      </template>
    </el-dialog>

    <!-- 角色成员弹窗 -->
    <el-dialog v-model="roleMembersDialogVisible" :title="roleMembersRole + '成员列表'" width="500px">
      <el-table :data="roleMembersList" style="width: 100%">
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="studentId" label="学号" />
        <el-table-column prop="school" label="学校" />
        <el-table-column prop="grade" label="年级" />
        <el-table-column prop="major" label="专业" />
        <el-table-column prop="email" label="邮箱" />
      </el-table>
    </el-dialog>

    <!-- 团队公告弹窗 -->
    <el-dialog v-model="announceDialogVisible" title="团队公告" width="500px">
      <div>这里可以发布和查看团队公告内容。</div>
      <template #footer>
        <el-button @click="announceDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 操作日志弹窗 -->
    <el-dialog v-model="logDialogVisible" title="操作日志" width="500px">
      <el-timeline>
        <el-timeline-item
            v-for="(log, idx) in teamLogs"
            :key="idx"
            :timestamp="log.time"
            :color="log.type === 'danger' ? '#f56c6c' : '#409eff'"
        >
          {{ log.content }}
        </el-timeline-item>
      </el-timeline>
      <template #footer>
        <el-button @click="logDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  ref, reactive, computed, onMounted, nextTick, watch
} from 'vue'
import { ElMessage } from 'element-plus'
import ProUploadFile from './CommonUpload/ProUploadFile.vue'
import ModernCard from '@/components/ModernCard.vue'
import { Close } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { ref as vueRef } from 'vue'

// 角色选项
const roles = [
  { value: '队长', label: '队长' },
  { value: '副队长', label: '副队长' },
  { value: '队员', label: '队员' },
  { value: '实习生', label: '实习生' }
]

// 默认标签
const defaultTags = [
  '技术', '设计', '产品', '运营', '市场', '销售',
  '研发', '测试', '运维', '客服', '人事', '财务'
]

// 队伍状态选项
const statusOptions = [
  { value: '待审核', label: '待审核' },
  { value: '已通过', label: '已通过' },
  { value: '已拒绝', label: '已拒绝' }
]

// 模拟赛事列表
const competitionList = [
  { value: '2024数学建模竞赛', label: '2024数学建模竞赛' },
  { value: '2024程序设计大赛', label: '2024程序设计大赛' }
]

// 队伍数据
const teams = ref([
  {
    id: 'T001',
    name: '创新先锋队',
    code: 'T001',
    leader: '张三',
    members: [
      { id: 1, name: '张三', role: '队长' },
      { id: 2, name: '李四', role: '队员' },
      { id: 3, name: '王五', role: '队员' }
    ],
    competition: '2024数学建模竞赛',
    status: '待审核',
    remark: '本队擅长建模与算法'
  },
  {
    id: 'T002',
    name: '梦想启航队',
    code: 'T002',
    leader: '王五',
    members: [
      { id: 4, name: '王五', role: '队长' },
      { id: 5, name: '赵六', role: '队员' }
    ],
    competition: '2024程序设计大赛',
    status: '已通过',
    remark: ''
  }
])

const searchQuery = ref('')
const filterByMemberCount = ref('all')
const selectedTeam = ref(null)
const teamDialogVisible = ref(false)
const teamFormRef = ref(null)
const teamForm = reactive({
  id: '',
  name: '',
  code: '',
  leader: '',
  competition: '',
  status: '',
  remark: ''
})
const currentTeamId = ref(null)

const filteredTeams = computed(() => {
  let result = teams.value
  if (searchQuery.value) {
    result = result.filter(team =>
        team.name.includes(searchQuery.value) ||
        team.code.includes(searchQuery.value) ||
        team.leader.includes(searchQuery.value)
    )
  }
  if (filterByMemberCount.value !== 'all') {
    const [min, max] = filterByMemberCount.value === '7+' ? [7, Infinity] : filterByMemberCount.value.split('-').map(Number)
    result = result.filter(team => team.members.length >= min && team.members.length <= max)
  }
  return result
})

const selectTeam = (team) => {
  selectedTeam.value = team
}

const openAddTeamDialog = () => {
  Object.assign(teamForm, {
    id: '',
    name: '',
    code: '',
    leader: '',
    competition: '',
    status: '',
    remark: ''
  })
  currentTeamId.value = null
  teamDialogVisible.value = true
}

const openEditTeamDialog = () => {
  if (!selectedTeam.value) return
  Object.assign(teamForm, {
    id: selectedTeam.value.id,
    name: selectedTeam.value.name,
    code: selectedTeam.value.code,
    leader: selectedTeam.value.leader,
    competition: selectedTeam.value.competition,
    status: selectedTeam.value.status,
    remark: selectedTeam.value.remark
  })
  currentTeamId.value = selectedTeam.value.id
  teamDialogVisible.value = true
}

const submitTeamForm = () => {
  teamFormRef.value?.validate(valid => {
    if (valid) {
      if (currentTeamId.value) {
        // 更新
        const idx = teams.value.findIndex(t => t.id === currentTeamId.value)
        if (idx !== -1) {
          teams.value[idx] = {
            ...teams.value[idx],
            ...teamForm,
            members: teams.value[idx].members
          }
        }
      } else {
        // 新增
        const newId = 'T' + (Date.now() % 100000)
        teams.value.push({
          ...teamForm,
          id: newId,
          code: teamForm.code || newId,
          leader: teamForm.leader,
          members: [],
          status: teamForm.status || '待审核',
          remark: teamForm.remark
        })
      }
      ElMessage.success('保存成功')
      teamDialogVisible.value = false
    }
  })
}

const formatStatus = (status) => {
  const map = { '待审核': 'warning', '已通过': 'success', '已拒绝': 'danger' }
  return map[status] || 'info'
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

const getMemberAvatar = (member) => {
  if (member.avatar) return member.avatar
  return `https://api.dicebear.com/7.x/avataaars/svg?seed=${member.name}`
}

const tableRowClassName = ({ row }) => {
  if (row.role === '队长') return 'captain-row'
  if (row.role === '副队长') return 'vice-captain-row'
  return ''
}

// 队员角色选项
const memberRoles = [
  { value: '队长', label: '队长' },
  { value: '队员', label: '队员' },
  { value: '替补', label: '替补' }
]

// 队员表单数据
const memberDialogVisible = ref(false)
const memberFormRef = ref(null)
const memberForm = reactive({
  id: '',
  avatar: '',
  name: '',
  studentId: '',
  school: '',
  grade: '',
  major: '',
  phone: '',
  email: '',
  role: '队员',
  bio: ''
})
const currentMemberId = ref(null)

// 队员表单校验
const memberRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  studentId: [{ required: true, message: '请输入学号', trigger: 'blur' }],
  school: [{ required: true, message: '请输入学校', trigger: 'blur' }],
  grade: [{ required: true, message: '请输入年级', trigger: 'blur' }],
  major: [{ required: true, message: '请输入专业', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }]
}

// 队员操作
const openAddMemberDialog = () => {
  Object.assign(memberForm, {
    id: '',
    avatar: '',
    name: '',
    studentId: '',
    school: '',
    grade: '',
    major: '',
    phone: '',
    email: '',
    role: '队员',
    bio: ''
  })
  currentMemberId.value = null
  memberDialogVisible.value = true
}
const editMember = (member) => {
  Object.assign(memberForm, { ...member })
  currentMemberId.value = member.id
  memberDialogVisible.value = true
}
const saveMember = () => {
  memberFormRef.value?.validate(valid => {
    if (!valid) return
    if (!selectedTeam.value) return
    if (currentMemberId.value) {
      // 更新
      const idx = selectedTeam.value.members.findIndex(m => m.id === currentMemberId.value)
      if (idx !== -1) {
        selectedTeam.value.members[idx] = { ...memberForm, id: currentMemberId.value }
      }
      ElMessage.success('队员信息更新成功')
    } else {
      // 新增
      const newId = Date.now()
      selectedTeam.value.members.push({ ...memberForm, id: newId })
      ElMessage.success('队员添加成功')
    }
    memberDialogVisible.value = false
  })
}
const deleteMember = (member) => {
  if (!selectedTeam.value) return
  selectedTeam.value.members = selectedTeam.value.members.filter(m => m.id !== member.id)
  ElMessage.success('队员删除成功')
}

const viewMemberDetail = (member) => {
  detailMember.value = { ...member }
  memberDetailVisible.value = true
}

const openDeleteTeamDialog = () => {
  if (!selectedTeam.value) return
  isDeletingTeam.value = true
  deleteDialogVisible.value = true
}

const submitMemberForm = () => {
  memberFormRef.value?.validate(valid => {
    if (valid) {
      if (!selectedTeam.value) return

      const memberData = { ...memberForm }

      if (!memberData.id) {
        // 添加新成员
        memberData.id = Date.now()
        memberData.joinedAt = new Date().toISOString()
        selectedTeam.value.members.push(memberData)
        ElMessage.success('成员添加成功')
      } else {
        // 更新现有成员
        const index = selectedTeam.value.members.findIndex(m => m.id === memberData.id)
        if (index !== -1) {
          selectedTeam.value.members[index] = memberData
          ElMessage.success('成员信息更新成功')
        }
      }

      memberDialogVisible.value = false
      nextTick(() => {
        initChart()
      })
    }
  })
}

const confirmDelete = () => {
  if (isDeletingTeam.value) {
    // 删除团队
    teams.value = teams.value.filter(t => t.id !== selectedTeam.value.id)
    ElMessage.success('团队删除成功')
    selectedTeam.value = null
  } else {
    // 删除成员
    if (selectedTeam.value) {
      selectedTeam.value.members = selectedTeam.value.members.filter(
          m => m.id !== memberForm.id
      )
      ElMessage.success('成员删除成功')
      nextTick(() => {
        initChart()
      })
    }
  }
  deleteDialogVisible.value = false
}

const handleMemberAvatarChange = (file) => {
  const reader = new FileReader()
  reader.onload = e => {
    memberForm.avatar = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

const getUnreadMessages = () => {
  return Math.floor(Math.random() * 10)
}

const chartRef = ref(null)
let chartInstance = null
const roleMembersDialogVisible = ref(false)
const roleMembersList = ref([])
const roleMembersRole = ref('')

const showRoleMembers = (role) => {
  if (!selectedTeam.value) return
  roleMembersRole.value = role
  roleMembersList.value = selectedTeam.value.members.filter(m => m.role === role)
  roleMembersDialogVisible.value = true
}

const initChart = () => {
  if (!selectedTeam.value || !chartRef.value) return
  if (chartInstance) {
    chartInstance.dispose()
  }
  chartInstance = echarts.init(chartRef.value)
  // 准备数据
  const roleData = {}
  selectedTeam.value.members.forEach(member => {
    roleData[member.role] = (roleData[member.role] || 0) + 1
  })
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '成员角色',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: Object.entries(roleData).map(([name, value]) => ({
          name,
          value
        }))
      }
    ]
  }
  chartInstance.setOption(option)

  // 添加点击事件
  chartInstance.off('click')
  chartInstance.on('click', function(params) {
    if (params.seriesType === 'pie') {
      showRoleMembers(params.name)
    }
  })
}

const isActive = (team) => selectedTeam.value && selectedTeam.value.id === team.id

const viewMemberExperience = (member) => {
  // 预留：后续可 emit 或路由跳转到 StudentPortrait.vue 并传递 member 信息
  // 例如：router.push({ name: 'StudentPortrait', params: { id: member.id } })
  ElMessage.info('后续将支持动态查看队员经历')
}

// 队员经历编辑相关
const experienceDialogVisible = ref(false)
const experienceFormRef = ref(null)
const experienceForm = reactive({
  index: null, // 编辑时用
  competitionName: '',
  time: '',
  award: '',
  role: '',
  description: '',
  certificate: '' // 奖状文件url
})
const experienceRules = {
  competitionName: [{ required: true, message: '请输入比赛名称', trigger: 'blur' }],
  time: [{ required: true, message: '请选择时间', trigger: 'change' }],
  award: [{ required: true, message: '请输入奖项', trigger: 'blur' }],
  role: [{ required: true, message: '请输入角色', trigger: 'blur' }],
  certificate: [{ required: true, message: '请上传奖状文件', trigger: 'change' }]
}
const currentMemberForExp = ref(null)
const openAddExperience = (member) => {
  Object.assign(experienceForm, {
    index: null,
    competitionName: '',
    time: '',
    award: '',
    role: '',
    description: '',
    certificate: ''
  })
  currentMemberForExp.value = member
  experienceDialogVisible.value = true
}
const openEditExperience = (member, exp, idx) => {
  Object.assign(experienceForm, { ...exp, index: idx })
  currentMemberForExp.value = member
  experienceDialogVisible.value = true
}
const saveExperience = () => {
  experienceFormRef.value?.validate(valid => {
    if (!valid) return
    const exp = { ...experienceForm }
    if (experienceForm.index !== null) {
      // 编辑
      currentMemberForExp.value.experiences[experienceForm.index] = exp
    } else {
      // 新增
      if (!currentMemberForExp.value.experiences) currentMemberForExp.value.experiences = []
      currentMemberForExp.value.experiences.push(exp)
    }
    experienceDialogVisible.value = false
    ElMessage.success('保存成功')
  })
}
const deleteExperience = (member, idx) => {
  member.experiences.splice(idx, 1)
  ElMessage.success('已删除经历')
}
const handleCertificateUpload = (fileList) => {
  if (fileList && fileList.length > 0) {
    experienceForm.certificate = fileList[0].url || fileList[0].response?.data || ''
  } else {
    experienceForm.certificate = ''
  }
}

// 邮箱相关
const showMailCard = vueRef(false)
const mailFormRef = vueRef(null)
const mailForm = reactive({
  to: '',
  subject: '',
  content: ''
})
const openMailCard = () => {
  mailForm.to = selectedTeam.value?.members?.map(m => m.email).filter(Boolean).join(',') || ''
  mailForm.subject = ''
  mailForm.content = ''
  showMailCard.value = true
}
const sendMail = () => {
  // 这里只做前端演示，实际可对接后端API
  if (!mailForm.to || !mailForm.subject || !mailForm.content) {
    ElMessage.error('请填写完整邮件信息')
    return
  }
  ElMessage.success('邮件已发送（模拟）')
  showMailCard.value = false
}

const mailTab = ref('team')
// 组队通知模拟数据
const teamNotices = ref([
  { content: '[申请加入团队]，bl申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-21 02:32:18', status: '待处理' },
  { content: '[申请加入团队],C5CkYB4q申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 23:49:50', status: '已处理' },
  { content: '[申请加入团队],xxEOFgNl申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 23:34:00', status: '已处理' },
  { content: '[申请加入团队],userA申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 22:10:00', status: '待处理' },
  { content: '[申请加入团队],userB申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 21:50:00', status: '待处理' },
  { content: '[申请加入团队],userC申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 21:30:00', status: '已处理' },
  { content: '[申请加入团队],userD申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 21:10:00', status: '已处理' },
  { content: '[申请加入团队],userE申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 20:50:00', status: '待处理' },
  { content: '[申请加入团队],userF申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 20:30:00', status: '已处理' },
  { content: '[申请加入团队],userG申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 20:10:00', status: '待处理' },
  { content: '[申请加入团队],userH申请加入"AI辅助高校竞赛组织管理实践系统应用"YTEibF2IV团队', time: '2025-06-20 19:50:00', status: '已处理' }
])
const systemMessages = ref([
  { content: '系统维护通知：6月22日凌晨维护', time: '2025-06-20 20:00:00', status: '已读' },
  { content: '您的团队"创新先锋队"已通过审核', time: '2025-06-20 19:00:00', status: '已读' },
  { content: '您的团队"梦想启航队"有新成员加入', time: '2025-06-20 18:30:00', status: '已读' },
  { content: '赛事"2024数学建模竞赛"报名截止提醒', time: '2025-06-20 18:00:00', status: '已读' },
  { content: '系统检测到异常登录，请注意账号安全', time: '2025-06-20 17:30:00', status: '已读' },
  { content: '您的资料已完善', time: '2025-06-20 17:00:00', status: '已读' },
  { content: '团队"创新先锋队"已被设为默认团队', time: '2025-06-20 16:30:00', status: '已读' },
  { content: '您有一条新的系统通知', time: '2025-06-20 16:00:00', status: '已读' },
  { content: '团队"梦想启航队"成员信息已更新', time: '2025-06-20 15:30:00', status: '已读' },
  { content: '赛事"2024程序设计大赛"即将开始', time: '2025-06-20 15:00:00', status: '已读' },
  { content: '系统消息测试数据', time: '2025-06-20 14:30:00', status: '已读' }
])
const handleNoticeAgree = (row) => {
  row.status = '已同意'
  ElMessage.success('已同意该申请')
}
const handleNoticeReject = (row) => {
  row.status = '已拒绝'
  ElMessage.warning('已拒绝该申请')
}

const hasSelection = computed(() => {
  if (mailTab.value === 'team') {
    return teamNotices.value.some(item => item._checked)
  } else {
    return systemMessages.value.some(item => item._checked)
  }
})
const deleteSelectedMessages = () => {
  if (mailTab.value === 'team') {
    teamNotices.value = teamNotices.value.filter(item => !item._checked)
  } else {
    systemMessages.value = systemMessages.value.filter(item => !item._checked)
  }
  ElMessage.success('已删除选中消息')
}
const selectAllTeamNotices = () => {
  const allChecked = teamNotices.value.every(item => item._checked)
  teamNotices.value.forEach(item => { item._checked = !allChecked })
}
const selectAllSystemMessages = () => {
  const allChecked = systemMessages.value.every(item => item._checked)
  systemMessages.value.forEach(item => { item._checked = !allChecked })
}
const onRowCheckChange = (type) => {
  // 触发hasSelection更新
}

// 生命周期钩子
onMounted(() => {
  // 更新当前时间
  setInterval(() => {
    currentTime.value = new Date().toLocaleString()
  }, 1000)

  // 模拟加载数据
  setTimeout(() => {
    isLoading.value = false
    if (selectedTeam.value) {
      nextTick(() => {
        initChart()
      })
    }
  }, 800)
})

// 监听器
watch(selectedTeam, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initChart()
    })
  }
})

// 监听窗口大小变化，更新图表
watch(() => window.innerWidth, () => {
  if (selectedTeam.value) {
    nextTick(() => {
      initChart()
    })
  }
})

const currentStep = computed(() => {
  if (!selectedTeam.value) return 0
  switch(selectedTeam.value.status) {
    case '待审核': return 1
    case '已通过': return 2
    case '已报名': return 3
    case '已结束': return 4
    default: return 0
  }
})

const showStepDetail = (step) => {
  const steps = ['组建团队','审核中','报名赛事','参赛中','获奖/结束']
  ElMessage.info(steps[step])
}

const memberDetailVisible = ref(false)
const deleteDialogVisible = ref(false)
const isDeletingTeam = ref(false)
const isLoading = ref(false)
const currentTime = ref('')

const handleSearch = () => {
  // 实现搜索逻辑
  ElMessage.info('搜索逻辑尚未实现')
}

// 渐进式团队切换
const showSidebar = ref(false)

// 公告、二维码、操作日志弹窗控制
const announceDialogVisible = ref(false)
const logDialogVisible = ref(false)
const qrcodeUrl = ref('https://api.qrserver.com/v1/create-qr-code/?size=160x160&data=team-invite-demo')
const inviteCode = ref('ABC123')
const openAnnounceDialog = () => { announceDialogVisible.value = true }
// 操作日志示例
const teamLogs = ref([
  { time: '2024-06-22 10:00', content: '张三创建了团队', type: 'info' },
  { time: '2024-06-22 10:10', content: '李四加入团队', type: 'info' },
  { time: '2024-06-22 10:20', content: '团队信息被编辑', type: 'info' },
  { time: '2024-06-22 10:30', content: '王五被移除团队', type: 'danger' }
])
// 成员卡片"更多"操作
const transferCaptain = (member) => {
  ElMessage.success(`已将队长转让给 ${member.name}`)
}
const resetPassword = (member) => {
  ElMessage.success(`已重置 ${member.name} 的密码`)
}

// 二维码下载与邀请码复制功能
const qrcodeImgRef = vueRef(null)
const downloadQrcode = () => {
  const img = qrcodeImgRef.value
  if (!img) {
    ElMessage.error('二维码图片未加载')
    return
  }
  const url = img.src
  const a = document.createElement('a')
  a.href = url
  a.download = 'team-qrcode.png'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  ElMessage.success('二维码已下载')
}

const copyInviteCode = () => {
  if (!inviteCode.value) {
    ElMessage.error('邀请码为空')
    return
  }
  if (navigator.clipboard) {
    navigator.clipboard.writeText(inviteCode.value).then(() => {
      ElMessage.success('邀请码已复制')
    }).catch(() => {
      ElMessage.error('复制失败')
    })
  } else {
    // 兼容性处理
    const input = document.createElement('input')
    input.value = inviteCode.value
    document.body.appendChild(input)
    input.select()
    document.execCommand('copy')
    document.body.removeChild(input)
    ElMessage.success('邀请码已复制')
  }
}

// 新增转让队长弹窗逻辑
const openTransferCaptainDialog = () => {
  // 这里可以添加转让队长的逻辑
  ElMessage.info('转让队长功能尚未实现')
}
</script>

<style scoped>
.container {
  max-width: 2000px;
  margin: 0 auto;
  min-height: calc(100vh - 64px);
  background-color: #fff;
  padding: 24px 24px 32px 24px;
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
}
.team-switch-list {
  width: 220px;
  margin-right: 24px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: #f6faff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(64,158,255,0.08);
  padding: 18px 12px 18px 12px;
  height: 70vh;
  min-height: 400px;
}
.team-switch-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 12px;
  color: #333;
}
.team-switch-btns-sticky {
  display: flex;
  gap: 12px;
  margin-bottom: 18px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}
.team-switch-card-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 18px;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}
.team-switch-card-optim {
  border-radius: 14px !important;
  box-shadow: 0 2px 12px rgba(64,158,255,0.10) !important;
  border: 2px solid #409eff !important;
  cursor: pointer;
  background: #409eff !important;
  color: #fff !important;
  margin-bottom: 0 !important;
  transition: box-shadow 0.2s, border 0.2s, transform 0.2s, background 0.2s;
}
.team-switch-card-optim .team-card-title,
.team-switch-card-optim .team-card-info {
  color: #fff !important;
}
.team-switch-card-optim .team-card-info {
  font-size: 13px;
  margin-bottom: 2px;
  opacity: 0.92;
}
.team-switch-card-optim.active, .team-switch-card-optim:hover {
  border: 2px solid #156ecf !important;
  box-shadow: 0 4px 18px rgba(64,158,255,0.18) !important;
  background: #156ecf !important;
  transform: translateY(-2px) scale(1.04);
}
.team-card-title {
  font-size: 17px;
  font-weight: 600;
  color: #222;
  margin-bottom: 4px;
}
.team-card-info {
  font-size: 13px;
  margin-bottom: 2px;
  opacity: 0.92;
}
.main-content {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  margin-left: 0;
}
.loading-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.team-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}
.team-card {
  transition: box-shadow 0.2s, border 0.2s, background 0.2s, transform 0.15s;
  cursor: pointer;
  border-radius: 14px;
  background: #fff;
  border: 1.5px solid #f0f0f0;
}
.team-card:hover {
  box-shadow: 0 6px 24px rgba(64,158,255,0.13);
  background: #f5faff;
  border: 1.5px solid #409eff;
  transform: translateY(-2px) scale(1.02);
}
.team-card-active {
  border: 2px solid var(--el-color-primary);
  background: #e6f7ff;
  box-shadow: 0 6px 24px rgba(64,158,255,0.18);
}
.team-meta {
  padding: 12px 0;
}
.stats-card {
  height: 100%;
  transition: all 0.3s ease;
}
.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.stats-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bg-primary-light {
  background-color: rgba(64, 158, 255, 0.1);
}
.bg-success-light {
  background-color: rgba(103, 194, 58, 0.1);
}
.bg-warning-light {
  background-color: rgba(230, 162, 60, 0.1);
}
.text-primary {
  color: var(--el-color-primary);
}
.text-success {
  color: var(--el-color-success);
}
.text-warning {
  color: var(--el-color-warning);
}
.text-danger {
  color: var(--el-color-danger);
}
.chart-container {
  height: 300px;
  width: 100%;
}
.captain-row {
  background-color: rgba(64, 158, 255, 0.05);
}
.vice-captain-row {
  background-color: rgba(103, 194, 58, 0.05);
}
.avatar-uploader {
  text-align: center;
  cursor: pointer;
}
.member-detail {
  padding: 20px;
}
:deep(.el-descriptions__label) {
  width: 120px;
  justify-content: flex-end;
}
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}
:deep(.el-dialog__body) {
  padding: 20px 30px;
}
.member-card {
  min-height: 260px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  border-radius: 14px;
  padding: 24px 24px 16px 24px;
  background: #fff;
}
.avatar-modern {
  border: 3px solid #e6f0fa;
  box-shadow: 0 2px 8px rgba(64,158,255,0.08);
}
.modern-shadow {
  box-shadow: 0 2px 12px rgba(0,0,0,0.08) !important;
}
@media (max-width: 768px) {
  .member-card {
    min-height: 180px;
    padding: 12px 8px;
  }
}
.experience-list {
  background: #fff;
  border-radius: 8px;
  padding: 12px 16px 8px 16px;
  margin-top: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}
.exp-item {
  margin-bottom: 8px;
}
@media (max-width: 900px) {
  .container {
    flex-direction: column;
    padding: 12px 4px;
  }
  .team-switch-list {
    width: 100%;
    flex-direction: row;
    margin-right: 0;
    margin-bottom: 16px;
    overflow-x: auto;
  }
  .team-switch-card {
    min-width: 160px;
    margin-right: 12px;
    margin-bottom: 0;
  }
  .main-content {
    margin-left: 0;
  }
}
.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  letter-spacing: 1px;
}
.action-group {
  display: flex;
  gap: 12px;
}
.divider {
  height: 1px;
  background: #f0f0f0;
  margin: 32px 0 24px 0;
  border-radius: 1px;
}
.member-management {
  margin-bottom: 32px;
}
.team-visualization {
  margin-bottom: 32px;
}
@media (max-width: 900px) {
  .main-content {
    margin-left: 0 !important;
  }
  .member-card {
    padding: 12px 8px;
  }
  .experience-list {
    padding: 8px 8px 4px 8px;
    margin-left: 0;
  }
  .section-title {
    font-size: 17px;
    margin-bottom: 14px;
  }
}
:deep(.el-dialog) {
  max-width: 95vw;
}
:deep(.el-form-item) {
  margin-bottom: 18px;
}
:deep(.el-tag), .modern-tag {
  border-radius: 6px;
  font-size: 13px;
  padding: 0 12px;
  height: 28px;
  line-height: 26px;
}
.member-card {
  padding: 32px 32px 20px 32px;
}
.experience-list {
  margin-left: 12px;
  padding: 16px 20px 12px 20px;
}
.exp-timeline {
  margin-left: 8px;
}
.team-detail {
  padding: 24px 16px 0 16px;
}
@media (max-width: 900px) {
  .team-detail {
    padding: 8px 2px 0 2px;
  }
}
.mail-float-card {
  position: absolute;
  top: 24px;
  right: 24px;
  width: 600px;
  height: 520px;
  z-index: 100;
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
}
.mail-float-card-large {
  width: 600px;
  height: 520px;
  display: flex;
  flex-direction: column;
}
.mail-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 12px;
}
.close-icon {
  cursor: pointer;
  font-size: 18px;
  color: #999;
  transition: color 0.2s;
}
.close-icon:hover {
  color: #f56c6c;
}
.empty-msg {
  text-align: center;
  color: #aaa;
  padding: 32px 0;
  font-size: 15px;
}
.mail-table-header {
  display: flex;
  align-items: center;
  font-weight: bold;
  background: #fafbfc;
  border-bottom: 1px solid #eee;
  padding: 8px 12px;
  position: sticky;
  top: 0;
  z-index: 2;
}
.mail-table-body {
  height: 370px;
  overflow-y: auto;
}
.mail-table-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 12px;
}
.mail-col-content {
  flex: 1 1 0;
  min-width: 200px;
  margin-right: 10px;
  word-break: break-all;
}
.mail-col-time {
  width: 160px;
  margin-right: 10px;
  color: #888;
  font-size: 13px;
}
.mail-col-action {
  width: 140px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.global-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 18px;
  align-items: center;
}
.search-input {
  flex: 1;
}
.welcome-empty {
  min-height: 400px;
  text-align: center;
}
.main-header {
  display: flex;
  align-items: center;
  gap: 14px;
  margin-bottom: 0;
  flex-wrap: wrap;
  justify-content: flex-end;
}
.team-drawer .el-drawer__body {
  padding: 0 0 16px 0;
  background: #fafdff;
  border-top-right-radius: 18px;
  border-bottom-right-radius: 18px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}
.drawer-header {
  padding: 0 0 8px 0;
  background: transparent;
  text-align: center;
}
.drawer-header-inner {
  background: #eaf4ff;
  border-radius: 16px;
  padding: 22px 0 14px 0;
  margin: 18px 18px 0 18px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(64,158,255,0.06);
}
.drawer-header-inner .font-bold {
  color: #156ecf;
}
.drawer-header-inner .text-gray-500 {
  color: #409eff;
}
.drawer-footer {
  padding: 12px 0 0 0;
  background: #fafdff;
  border-bottom-right-radius: 18px;
  display: flex;
  justify-content: center;
}
.drawer-create-btn {
  width: 90%;
  border-radius: 8px;
  font-weight: bold;
  font-size: 15px;
  letter-spacing: 1px;
  margin: 0 auto;
  box-shadow: 0 2px 8px rgba(64,158,255,0.08);
}
.team-switch-card-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding: 12px 16px 0 16px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  scrollbar-width: thin;
  scrollbar-color: #b3d8fd #fafdff;
}
.team-switch-card {
  display: flex;
  align-items: center;
  border-radius: 12px;
  background: #fff;
  border: 1.5px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(64,158,255,0.06);
  padding: 10px 14px;
  cursor: pointer;
  transition: box-shadow 0.18s, border 0.18s, background 0.18s, transform 0.15s;
  color: #333;
  min-width: 0;
}
.team-switch-card:hover {
  background: #e6f7ff;
  border: 1.5px solid #409eff;
  box-shadow: 0 4px 16px rgba(64,158,255,0.13);
  transform: translateY(-2px) scale(1.03);
}
.team-switch-card.active {
  background: #409eff;
  color: #fff;
  border: 2px solid #156ecf;
  box-shadow: 0 6px 24px rgba(64,158,255,0.18);
  font-weight: bold;
}
.team-switch-card.active .team-card-title,
.team-switch-card.active .team-card-info {
  color: #fff;
}
.team-switch-card.active .team-card-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.team-switch-card.active .team-card-info {
  font-size: 12px;
  opacity: 0.92;
  margin-bottom: 1px;
  color: #666;
}
.switch-team-btn {
  background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
  color: #fff;
  border-radius: 8px;
  font-weight: bold;
  font-size: 15px;
  letter-spacing: 1px;
  box-shadow: 0 2px 8px rgba(64,158,255,0.10);
  border: none;
  padding: 0 18px;
  display: flex;
  align-items: center;
}
.switch-team-btn .el-icon {
  font-size: 18px;
}
.mail-btn {
  border-radius: 8px;
  font-weight: bold;
  font-size: 15px;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
}
.mail-btn .el-icon {
  font-size: 18px;
}
.page-title-row {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-bottom: 18px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
  background: none;
  flex-wrap: wrap;
}
.page-title {
  text-align: left;
}
.page-title h1 {
  font-size: 22px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 0;
  letter-spacing: 1px;
  line-height: 1.2;
}
.page-desc {
  color: #909399;
  font-size: 14px;
  margin-top: 4px;
  margin-bottom: 0;
}
.announce-btn {
  border-radius: 8px;
  font-weight: bold;
  font-size: 15px;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  background: #eaf4ff;
  color: #156ecf;
  border: none;
  box-shadow: 0 2px 8px rgba(64,158,255,0.10);
}
.qrcode-btn {
  border-radius: 8px;
  font-weight: bold;
  font-size: 15px;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  background: #fffbe6;
  color: #e6a23c;
  border: none;
  box-shadow: 0 2px 8px rgba(230,162,60,0.10);
}
.log-btn {
  border-radius: 8px;
  font-weight: bold;
  font-size: 15px;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  background: #f4f4f5;
  color: #333;
  border: none;
  box-shadow: 0 2px 8px rgba(64,158,255,0.06);
}
.member-more-dropdown {
  position: absolute;
  top: 10px;
  right: 10px;
}
@media (max-width: 900px) {
  .page-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  .main-header {
    justify-content: flex-start;
    gap: 10px;
    margin-top: 8px;
  }
}
.more-btn {
  border-radius: 8px;
  font-weight: bold;
  font-size: 15px;
  letter-spacing: 1px;
  background: #f4f4f5;
  color: #333;
  border: none;
  box-shadow: 0 2px 8px rgba(64,158,255,0.06);
}
.more-actions-dropdown {
  margin-left: 4px;
}
.team-visualization-row {
  display: flex;
  gap: 32px;
  align-items: stretch;
  margin-bottom: 0;
}
.team-visualization, .team-qrcode-box {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.team-qrcode-box {
  max-width: 380px;
  min-width: 260px;
  align-items: stretch;
  padding: 0;
}
.visual-card, .qrcode-card {
  height: 100%;
  min-height: 340px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  box-shadow: 0 2px 8px rgba(64,158,255,0.06);
  border-radius: 14px;
  border: 1px solid #e4e7ed;
  padding: 32px 24px 24px 24px;
  background: #fff;
  box-sizing: border-box;
}
.qrcode-card {
  text-align: left;
  position: relative;
  padding: 32px 24px 24px 24px;
}
.qrcode-title {
  text-align: left !important;
  margin-bottom: 24px !important;
}
.qrcode-content-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.qrcode-img-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 8px;
  margin-bottom: 16px;
}
.qrcode-img {
  width: 180px;
  height: 180px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(64,158,255,0.10);
  background: #fafdff;
}
.qrcode-invite {
  font-size: 16px;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}
.invite-code {
  letter-spacing: 2px;
  color: #156ecf;
}
.qrcode-btns {
  justify-content: center;
  margin-top: 0;
  display: flex;
  gap: 12px;
}
@media (max-width: 900px) {
  .team-visualization-row {
    flex-direction: column;
    gap: 18px;
  }
  .visual-card, .qrcode-card {
    padding: 18px 8px 12px 8px;
    min-height: 220px;
  }
}

.team-header-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
  gap: 32px;
}
.team-info {
  display: flex;
  align-items: center;
  gap: 18px;
}
.team-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 6px;
}
.team-desc {
  color: #909399;
  font-size: 15px;
  max-width: 320px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.team-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}
.team-steps-row {
  margin-bottom: 32px;
  margin-top: 0;
}
.el-steps {
  background: #fff;
  padding: 12px 0;
  border-radius: 8px;
}
.el-step__title.is-process {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.team-header-optimized {
  margin-bottom: 32px;
  padding-bottom: 18px;
  border-bottom: 1px solid #f0f0f0;
}
.team-header-actions {
  display: flex;
  gap: 18px;
  align-items: center;
}
.team-steps-optimized {
  margin-bottom: 32px;
  margin-top: 0;
  padding-top: 8px;
  padding-bottom: 8px;
}
.el-step__title.is-process {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}
.divider-after-steps {
  margin-top: -8px;
  margin-bottom: 24px;
  height: 1px;
  background: #f0f0f0;
  border-radius: 1px;
}
</style>    