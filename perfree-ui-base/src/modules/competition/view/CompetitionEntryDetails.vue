<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElUpload, ElButton, ElTag, ElDivider, ElDialog, ElTabs, ElTabPane, ElTable, ElTableColumn } from 'element-plus';
import { Upload, Plus, Edit, View, Delete, ArrowUp, ArrowDown, Search } from '@element-plus/icons-vue';

// 页面模式：create-创建，edit-编辑，view-查看
const pageMode = ref('create');
const loading = ref(false);
const formRef = ref(null);

// 作品基本信息
const entryForm = reactive({
  // 基本信息
  title: '', // 作品名称
  category: '', // 作品类别
  discipline: '', // 学科类别
  keywords: [], // 关键词
  abstract: '', // 作品摘要
  introduction: '', // 作品介绍
  innovation: '', // 创新点
  application: '', // 应用价值

  // 作者信息
  authors: [], // 作者列表

  // 作品材料
  mainFile: null, // 主文件
  attachments: [], // 附件列表
  videoLink: '', // 视频链接
  videoCode: '', // 视频提取码

  // 其他信息
  competition: '', // 所属赛事
  submissionDate: '', // 提交日期
  status: 'draft', // 状态：draft-草稿，submitted-已提交，reviewing-审核中，approved-已通过，rejected-已驳回
  remarks: '', // 备注
  teamLeaders: [], // 领队/教师列表 (包括指导教师)
});

// 学科类别选项
const disciplineOptions = [
  { label: '自然科学', value: 'natural_science' },
  { label: '工程技术', value: 'engineering' },
  { label: '人文社科', value: 'humanities' },
  { label: '艺术设计', value: 'art_design' },
  { label: '创新创业', value: 'innovation' },
];

// 作品类别选项
const categoryOptions = [
  { label: '论文', value: 'paper' },
  { label: '设计', value: 'design' },
  { label: '发明', value: 'invention' },
  { label: '软件', value: 'software' },
  { label: '其他', value: 'other' },
];

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入作品名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择作品类别', trigger: 'change' }],
  discipline: [{ required: true, message: '请选择学科类别', trigger: 'change' }],
  abstract: [{ required: true, message: '请输入作品摘要', trigger: 'blur' }],
  introduction: [{ required: true, message: '请输入作品介绍', trigger: 'blur' }],
  innovation: [{ required: true, message: '请输入创新点', trigger: 'blur' }],
  application: [{ required: true, message: '请输入应用价值', trigger: 'blur' }],
  mainFile: [{ required: true, message: '请上传主文件', trigger: 'change' }],
};

// 处理文件上传
const handleFileUpload = (file) => {
  // TODO: 实现文件上传逻辑
  console.log('上传文件:', file);
  entryForm.mainFile = file;
  return false; // 阻止自动上传
};

// 处理作者添加
const addAuthor = () => {
  entryForm.authors.push({
    name: '',
    role: 'member', // member-成员，leader-负责人
    studentId: '',
    major: '',
  });
};

// 处理作者删除
const removeAuthor = (index) => {
  entryForm.authors.splice(index, 1);
};

// 处理附件添加
const addAttachment = () => {
  entryForm.attachments.push({
    name: '',
    file: null,
    description: '',
  });
};

// 处理附件删除
const removeAttachment = (index) => {
  entryForm.attachments.splice(index, 1);
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    // TODO: 实现表单提交逻辑
    console.log('提交表单:', entryForm);

    ElMessage.success('提交成功！');
  } catch (error) {
    console.error('表单验证失败:', error);
    ElMessage.error('请检查表单填写是否正确');
  } finally {
    loading.value = false;
  }
};

// 保存草稿
const saveDraft = () => {
  // TODO: 实现草稿保存逻辑
  console.log('保存草稿:', entryForm);
  ElMessage.success('草稿保存成功！');
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 初始化数据
const initData = (id) => {
  // TODO: 根据ID获取作品详情
  console.log('获取作品详情:', id);
  // 模拟数据加载
  Object.assign(entryForm, {
    title: '“一触即达”大学生多功能融合生活服务平台',
    category: 'software',
    discipline: 'engineering',
    keywords: ['智慧校园', '生活服务', '小程序'],
    abstract: '本作品旨在打造一款集成校园生活各项服务的综合性平台，涵盖餐饮、学习、社交、出行等多个方面，提升大学生校园生活的便利性和效率。',
    introduction: '详细介绍本作品的背景、目标、技术栈、功能模块、用户群体等信息。',
    innovation: '本作品创新点在于其高度的集成性和个性化服务，通过大数据分析用户行为，智能推荐服务，并提供开放API接口，支持第三方服务接入。',
    application: '本作品可应用于各类高校，提升校园管理效率，丰富学生生活体验，具有广阔的市场前景和商业价值。',
    authors: [
      { name: '刘兆鹏', role: 'leader', studentId: '2312402100117', major: '软件工程' },
      { name: '胡灵达', role: 'member', studentId: '2312402100136', major: '数据科学与大数据技术' },
      { name: '郑晨皓', role: 'member', studentId: '2312402100139', major: '计算机科学与技术' },
    ],
    teamLeaders: [
      { id: 'T001', name: '王老师', role: 'instructor', unit: '计算机学院', contact: '13511112222' },
      { id: 'L001', name: '李领队', role: 'leader', unit: '教务处', contact: '13466667777' },
    ],
    mainFile: { name: '作品说明书.pdf', url: '/path/to/作品说明书.pdf' },
    attachments: [
      { name: '设计图.zip', file: null, description: '包含产品原型图和UI设计图' },
      { name: '演示视频.mp4', file: null, description: '作品演示视频' },
    ],
    videoLink: 'https://example.com/video',
    videoCode: 'abcde',
    competition: '2023年全国大学生创新创业大赛',
    submissionDate: '2023-05-20',
    status: 'submitted',
    remarks: '这是一些备注信息，例如评审意见或后续改进建议。',
  });
};

const newKeyword = ref(''); // 用于关键词添加

// 激活的Tab
const activeTab = ref('basicInfo');

// 领队管理弹窗可见性
const teamLeaderDialogVisible = ref(false);
// 弹窗内部的领队/教师列表（操作副本）
const dialogTeamLeaders = ref([]);

// 搜索相关
const searchKeyword = ref('');
const searchResults = ref([]); // 模拟搜索结果

// 领队/教师角色选项
const teamLeaderRoleOptions = [
  { label: '领队', value: 'leader' },
  { label: '指导教师', value: 'instructor' },
  { label: '辅助教师', value: 'assistant' },
];

// 打开领队管理弹窗
const openTeamLeaderDialog = () => {
  // 复制一份当前领队列表到弹窗内部进行操作
  dialogTeamLeaders.value = JSON.parse(JSON.stringify(entryForm.teamLeaders));
  teamLeaderDialogVisible.value = true;
  // 清空搜索框和搜索结果
  searchKeyword.value = '';
  searchResults.value = [];
};

// 模拟用户搜索
const searchUsers = () => {
  if (!searchKeyword.value) {
    searchResults.value = [];
    return;
  }
  // 模拟API调用，实际项目中这里会调用后端接口
  console.log(`搜索用户: ${searchKeyword.value}`);
  const dummyUsers = [
    { id: '1', name: '刘兆鹏', studentId: '2312402100117', role: 'member', unit: '计算机学院', contact: '13812345678' },
    { id: '2', name: '胡灵达', studentId: '2312402100136', role: 'member', unit: '软件学院', contact: '13987654321' },
    { id: '3', name: '郑晨皓', studentId: '2312402100139', role: 'member', unit: '设计学院', contact: '13700001111' },
    { id: '4', name: '许涵怡', studentId: '2312402180110', role: 'member', unit: '外国语学院', contact: '13622223333' },
    { id: 'T001', name: '王老师', studentId: 'T001', role: 'instructor', unit: '计算机学院', contact: '13544445555' },
    { id: 'L001', name: '李领队', studentId: 'L001', role: 'leader', unit: '教务处', contact: '13466667777' },
  ];

  searchResults.value = dummyUsers.filter(user =>
      (user.name.includes(searchKeyword.value) || user.studentId.includes(searchKeyword.value)) &&
      !dialogTeamLeaders.value.some(leader => leader.id === user.id) // 排除已添加的
  );
};

// 将选中的用户添加到弹窗内部列表（如果未重复）
const addSelectedUserToDialog = (user) => {
  // 检查是否已存在 - 搜索结果过滤已经做了，这里再做一层保险
  const exists = dialogTeamLeaders.value.some(leader => leader.id === user.id);
  if (exists) {
    ElMessage.warning(`${user.name} 已经存在于列表中。`);
    return;
  }
  // 弹窗中添加时，默认角色为"指导教师"，用户可自行修改
  dialogTeamLeaders.value.push({ ...user, role: user.role === 'member' ? '' : user.role || 'instructor' }); // 默认为instructor，如果搜索结果自带角色则使用
  ElMessage.success(`${user.name} 已添加到列表。`);
  // 清空搜索结果，方便用户继续搜索或关闭
  searchKeyword.value = '';
  searchResults.value = [];
};

// 从弹窗内部列表删除领队/教师
const removeTeamLeaderFromDialog = (index) => {
  dialogTeamLeaders.value.splice(index, 1);
  ElMessage.success('领队/教师已从列表中移除');
};

// 从已选标签中移除领队/教师
const removeTeamLeaderByTag = (id) => {
  const index = dialogTeamLeaders.value.findIndex(leader => leader.id === id);
  if (index > -1) {
    dialogTeamLeaders.value.splice(index, 1);
    ElMessage.success('领队/教师已从列表中移除');
  }
};

// 上移领队/教师
const moveTeamLeaderUp = (index) => {
  if (index > 0) {
    const item = dialogTeamLeaders.value[index];
    dialogTeamLeaders.value.splice(index, 1);
    dialogTeamLeaders.value.splice(index - 1, 0, item);
  }
};

// 下移领队/教师
const moveTeamLeaderDown = (index) => {
  if (index < dialogTeamLeaders.value.length - 1) {
    const item = dialogTeamLeaders.value[index];
    dialogTeamLeaders.value.splice(index, 1);
    dialogTeamLeaders.value.splice(index + 1, 0, item);
  }
};

// 确认领队管理弹窗，保存更改到主表单
const confirmTeamLeaderManagement = () => {
  entryForm.teamLeaders = JSON.parse(JSON.stringify(dialogTeamLeaders.value));
  teamLeaderDialogVisible.value = false;
  ElMessage.success('领队/教师信息已更新！');
};

// 取消领队管理弹窗，放弃更改
const cancelTeamLeaderManagement = () => {
  teamLeaderDialogVisible.value = false;
  ElMessage.info('已取消操作，领队/教师信息未保存。');
};

onMounted(() => {
  // 根据路由参数判断页面模式
  const route = window.location.pathname;
  if (route.includes('/edit/')) {
    pageMode.value = 'edit';
    const id = route.split('/').pop();
    initData(id);
  } else if (route.includes('/view/')) {
    pageMode.value = 'view';
    const id = route.split('/').pop();
    initData(id);
  } else {
    // 如果是create模式，也可以模拟一些初始数据
    // initData(''); // 或者不调用，保持表单为空
  }
});
</script>

<template>
  <div class="page">
    <div class="entry-details">
      <div class="page-header">
        <h1>{{ pageMode === 'create' ? '创建作品' : pageMode === 'edit' ? '编辑作品' : '作品详情' }}</h1>
        <div class="header-actions">
          <el-button v-if="pageMode !== 'view'" @click="saveDraft">保存草稿</el-button>
          <el-button v-if="pageMode !== 'view'" type="primary" @click="submitForm" :loading="loading">提交</el-button>
        </div>
      </div>

      <el-tabs v-model="activeTab" class="entry-tabs">
        <el-tab-pane label="作品基本信息" name="basicInfo">
          <el-form
              ref="formRef"
              :model="entryForm"
              :rules="rules"
              label-width="120px"
              :disabled="pageMode === 'view'"
              class="form-container"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <div class="section-header">
                <h2>基本信息</h2>
              </div>
              <el-form-item label="作品名称" prop="title">
                <el-input v-model="entryForm.title" placeholder="请输入作品名称" />
              </el-form-item>

              <el-form-item label="作品类别" prop="category">
                <el-select v-model="entryForm.category" placeholder="请选择作品类别" class="full-width">
                  <el-option
                      v-for="option in categoryOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="学科类别" prop="discipline">
                <el-select v-model="entryForm.discipline" placeholder="请选择学科类别" class="full-width">
                  <el-option
                      v-for="option in disciplineOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="关键词" prop="keywords">
                <div class="keywords-input-group">
                  <el-tag
                      v-for="(keyword, index) in entryForm.keywords"
                      :key="index"
                      closable
                      @close="entryForm.keywords.splice(index, 1)"
                      class="keyword-tag"
                  >
                    {{ keyword }}
                  </el-tag>
                  <el-input
                      v-if="pageMode !== 'view'"
                      v-model="newKeyword"
                      @keyup.enter="entryForm.keywords.push(newKeyword); newKeyword = ''"
                      placeholder="输入关键词后按回车添加"
                      class="keyword-input"
                  />
                </div>
              </el-form-item>

              <el-form-item label="作品摘要" prop="abstract">
                <el-input
                    v-model="entryForm.abstract"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入作品摘要"
                />
              </el-form-item>
            </div>

            <!-- 作品详情 -->
            <div class="form-section">
              <div class="section-header">
                <h2>作品详情</h2>
              </div>
              <el-form-item label="作品介绍" prop="introduction">
                <el-input
                    v-model="entryForm.introduction"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入作品介绍"
                />
              </el-form-item>

              <el-form-item label="创新点" prop="innovation">
                <el-input
                    v-model="entryForm.innovation"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入创新点"
                />
              </el-form-item>

              <el-form-item label="应用价值" prop="application">
                <el-input
                    v-model="entryForm.application"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入应用价值"
                />
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="作者与指导教师" name="authorsAndInstructors">
          <el-form
              ref="formRef"
              :model="entryForm"
              :rules="rules"
              label-width="120px"
              :disabled="pageMode === 'view'"
              class="form-container"
          >
            <!-- 作者信息 -->
            <div class="form-section">
              <div class="section-header">
                <h2>作者信息</h2>
                <el-button
                    v-if="pageMode !== 'view'"
                    type="primary"
                    icon="Plus"
                    size="small"
                    @click="addAuthor"
                >
                  添加作者
                </el-button>
              </div>
              <div v-for="(author, index) in entryForm.authors" :key="index" class="author-item">
                <el-form-item :label="index === 0 ? '作者' : ''" :prop="'authors.' + index + '.name'">
                  <el-input v-model="author.name" placeholder="作者姓名" />
                </el-form-item>
                <el-form-item label="角色">
                  <el-select v-model="author.role" class="small-select">
                    <el-option label="负责人" value="leader" />
                    <el-option label="成员" value="member" />
                  </el-select>
                </el-form-item>
                <el-form-item label="学号">
                  <el-input v-model="author.studentId" placeholder="学号" />
                </el-form-item>
                <el-form-item label="专业">
                  <el-input v-model="author.major" placeholder="专业" />
                </el-form-item>
                <el-button
                    v-if="pageMode !== 'view'"
                    type="danger"
                    icon="Delete"
                    circle
                    @click="removeAuthor(index)"
                    class="delete-button"
                />
              </div>
            </div>

            <!-- 领队/教师管理入口 -->
            <div class="form-section">
              <div class="section-header">
                <h2>领队/指导教师管理</h2>
                <el-button
                    v-if="pageMode !== 'view'"
                    type="primary"
                    icon="Edit"
                    size="small"
                    @click="openTeamLeaderDialog"
                >
                  管理领队与指导教师
                </el-button>
              </div>
              <el-table :data="entryForm.teamLeaders" style="width: 100%; margin-top: 20px;" border v-if="entryForm.teamLeaders.length > 0">
                <el-table-column type="index" label="次序" width="80" />
                <el-table-column prop="name" label="姓名" width="150" />
                <el-table-column prop="role" label="角色" width="120">
                  <template #default="scope">
                    {{ teamLeaderRoleOptions.find(opt => opt.value === scope.row.role)?.label || scope.row.role }}
                  </template>
                </el-table-column>
                <el-table-column prop="unit" label="单位" />
                <el-table-column prop="contact" label="联系方式" />
              </el-table>
              <p v-else class="no-data-tip">暂无领队/指导教师信息，请点击"管理领队与指导教师"添加。</p>
            </div>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="作品材料" name="materials">
          <el-form
              ref="formRef"
              :model="entryForm"
              :rules="rules"
              label-width="120px"
              :disabled="pageMode === 'view'"
              class="form-container"
          >
            <!-- 作品材料 -->
            <div class="form-section">
              <div class="section-header">
                <h2>作品材料</h2>
              </div>
              <el-form-item label="主文件" prop="mainFile">
                <el-upload
                    class="upload-demo"
                    action="#"
                    :auto-upload="false"
                    :on-change="handleFileUpload"
                >
                  <el-button type="primary">选择文件</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持PDF、Word、PPT等格式，文件大小不超过50MB
                    </div>
                  </template>
                </el-upload>
                <span v-if="entryForm.mainFile" class="uploaded-file-name">已选择: {{ entryForm.mainFile.name }}</span>
              </el-form-item>

              <el-form-item label="视频材料">
                <el-input v-model="entryForm.videoLink" placeholder="视频网盘链接" />
                <el-input v-model="entryForm.videoCode" placeholder="视频提取码" style="margin-top: 10px;" />
              </el-form-item>

              <el-form-item label="附件">
                <div v-for="(attachment, index) in entryForm.attachments" :key="index" class="attachment-item">
                  <el-input v-model="attachment.name" placeholder="附件名称" class="attachment-name-input" />
                  <el-upload
                      class="upload-demo-small"
                      action="#"
                      :auto-upload="false"
                      :on-change="(file) => attachment.file = file"
                      :show-file-list="false"
                  >
                    <el-button type="primary" size="small">选择文件</el-button>
                  </el-upload>
                  <span v-if="attachment.file" class="uploaded-file-name">已选择: {{ attachment.file.name }}</span>
                  <el-input
                      v-model="attachment.description"
                      placeholder="附件说明"
                      class="attachment-description-input"
                  />
                  <el-button
                      v-if="pageMode !== 'view'"
                      type="danger"
                      icon="Delete"
                      circle
                      @click="removeAttachment(index)"
                      class="delete-button"
                  />
                </div>

                <el-button
                    v-if="pageMode !== 'view'"
                    type="primary"
                    icon="Plus"
                    @click="addAttachment"
                >
                  添加附件
                </el-button>
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="其他信息" name="otherInfo">
          <el-form
              ref="formRef"
              :model="entryForm"
              :rules="rules"
              label-width="120px"
              :disabled="pageMode === 'view'"
              class="form-container"
          >
            <!-- 其他信息 -->
            <div class="form-section">
              <div class="section-header">
                <h2>其他信息</h2>
              </div>
              <el-form-item label="备注">
                <el-input
                    v-model="entryForm.remarks"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入备注信息"
                />
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <!-- 领队管理弹窗 -->
      <el-dialog
          v-model="teamLeaderDialogVisible"
          title="领队/指导教师管理 (拖动以调整次序 - 暂不支持拖动)"
          width="800px"
          :close-on-click-modal="false"
          class="team-leader-dialog"
      >
        <div class="dialog-content">
          <div class="search-add-section">
            <div class="search-input-group">
              <el-input
                  v-model="searchKeyword"
                  placeholder="输入姓名或学号检索"
                  clearable
                  @keyup.enter="searchUsers"
                  class="search-user-input"
              >
                <template #append>
                  <el-button :icon="Search" @click="searchUsers">搜索</el-button>
                </template>
              </el-input>
            </div>
            <div class="search-results" v-if="searchResults.length > 0">
              <p>搜索结果:</p>
              <el-table :data="searchResults" style="width: 100%;" max-height="200" border class="search-results-table">
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="studentId" label="学号" width="150" />
                <el-table-column prop="unit" label="单位" />
                <el-table-column label="操作" width="80">
                  <template #default="scope">
                    <el-button link type="primary" :icon="Plus" @click="addSelectedUserToDialog(scope.row)">添加</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <p v-else-if="searchKeyword && searchResults.length === 0" class="no-search-results">未找到匹配用户。</p>
          </div>

          <el-divider>已选领队/指导教师 (可拖动调整次序 - 暂不支持拖动)</el-divider>

          <div class="selected-tags-container">
            <el-tag
                v-for="(leader, index) in dialogTeamLeaders"
                :key="leader.id || index"
                closable
                :disable-transitions="false"
                @close="removeTeamLeaderByTag(leader.id)"
                class="selected-leader-tag"
            >
              {{ leader.name }} {{ leader.studentId ? `(${leader.studentId})` : '' }}
            </el-tag>
            <span v-if="dialogTeamLeaders.length === 0" class="no-selected-tip">请在上方搜索并添加领队/指导教师</span>
          </div>

          <el-table :data="dialogTeamLeaders" style="width: 100%; margin-top: 20px;" border class="team-leader-table">
            <el-table-column type="index" label="次序" width="80" />
            <el-table-column prop="name" label="姓名" width="150" />
            <el-table-column prop="role" label="角色" width="120">
              <template #default="scope">
                <el-select v-model="scope.row.role" placeholder="选择角色" size="small">
                  <el-option
                      v-for="option in teamLeaderRoleOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位">
              <template #default="scope">
                <el-input v-model="scope.row.unit" placeholder="请输入单位" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="contact" label="联系方式">
              <template #default="scope">
                <el-input v-model="scope.row.contact" placeholder="请输入联系方式" size="small" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180">
              <template #default="scope">
                <el-button link type="primary" :icon="ArrowUp" @click="moveTeamLeaderUp(scope.$index)" :disabled="scope.$index === 0">上移</el-button>
                <el-button link type="primary" :icon="ArrowDown" @click="moveTeamLeaderDown(scope.$index)" :disabled="scope.$index === dialogTeamLeaders.length - 1">下移</el-button>
                <el-button link type="danger" :icon="Delete" @click="removeTeamLeaderFromDialog(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancelTeamLeaderManagement">取消</el-button>
            <el-button type="primary" @click="confirmTeamLeaderManagement">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<style scoped>
.page {
  padding: 20px;
  background: #fff; /* 页面背景色 */
  min-height: 100vh;
}

.entry-details {
  max-width: 1200px;
  margin: 0 auto;
  background: #fff; /* 主内容区背景也为白色 */
  padding: 0; /* 移除主内容区内边距，由内部section控制 */
  border-radius: 0; /* 移除圆角 */
  box-shadow: none; /* 移除阴影 */
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h1 {
  font-size: 20px; /* 调整H1字体大小 */
  font-weight: 600;
  color: #333; /* 统一颜色 */
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.entry-tabs {
  margin-top: 0; /* 标签页紧贴头部 */
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
  background-color: transparent; /* 标签页背景透明 */
  border-radius: 0;
  padding: 0;
  border-bottom: 1px solid #ebeef5; /* 标签底部细线 */
}

:deep(.el-tabs__nav-wrap) {
  display: flex;
  justify-content: flex-start;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

.form-container {
  padding: 24px;
  background: #fff; /* 整个表单区域为白色背景 */
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05); /* 给整个表单容器添加阴影 */
}

.form-section {
  margin-bottom: 32px;
  padding: 0; /* 移除内部section的内边距，由el-form-item控制 */
  background: transparent; /* 背景透明 */
  border-radius: 0;
  border: none; /* 移除边框 */
  box-shadow: none; /* 移除阴影 */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.section-header h2 {
  font-size: 20px; /* H2字体大小与H1类似 */
  font-weight: 600;
  color: #333;
  margin: 0;
}

.author-item,
.attachment-item {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 16px;
  padding: 0; /* 移除内边距 */
  background: transparent; /* 背景透明 */
  border-radius: 0;
  border: none; /* 移除边框 */
  box-shadow: none;
}

.author-item .el-form-item,
.attachment-item .el-form-item {
  margin-bottom: 0; /* 保持每行表单项底部无额外间距 */
  flex-basis: calc(25% - 15px);
  max-width: calc(25% - 15px);
}

.author-item .el-form-item:first-child {
  flex-basis: calc(25% - 15px);
  max-width: calc(25% - 15px);
}

.author-item .delete-button,
.attachment-item .delete-button {
  margin-left: auto;
}

.small-select {
  width: 100%;
}

.keywords-input-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.keyword-tag {
  margin-right: 0;
}

.keyword-input {
  flex-grow: 1;
  min-width: 150px;
  max-width: 250px;
}

.upload-demo {
  margin-top: 10px;
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.uploaded-file-name {
  margin-left: 10px;
  color: #606266;
  font-size: 14px;
}

.attachment-name-input,
.attachment-description-input {
  flex-basis: calc(33% - 15px);
  max-width: calc(33% - 15px);
}

.upload-demo-small {
  margin-top: 0;
}

.no-data-tip {
  color: #909399;
  text-align: center;
  margin-top: 20px;
}

/* Element Plus 组件默认样式调整 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner),
:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset; /* 默认边框 */
  border-radius: 4px;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover),
:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset; /* hover 边框 */
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-textarea__inner:focus),
:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #409eff inset; /* focus 边框 */
}

:deep(.el-button) {
  border-radius: 4px;
}

/* 弹窗内部样式 */
.team-leader-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.search-add-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.search-input-group {
  display: flex;
  margin-bottom: 15px;
}

.search-user-input {
  max-width: 400px;
}

.search-results {
  margin-top: 15px;
}

.search-results p {
  margin-bottom: 10px;
  font-weight: 500;
  color: #303133;
}

.search-results-table {
  margin-bottom: 15px;
}

.no-search-results {
  color: #909399;
  text-align: center;
  padding: 10px 0;
}

.selected-tags-container {
  min-height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  background-color: #f9f9f9;
}

.selected-leader-tag {
  margin-right: 0;
  background-color: #e0eaff;
  color: #409eff;
  border-color: #d9ecff;
}

.no-selected-tip {
  color: #909399;
  font-size: 14px;
}

.team-leader-table {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.full-width {
  width: 100%;
}
</style>
