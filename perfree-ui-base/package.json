{"name": "my-vue-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node build.js", "preview": "vite preview"}, "dependencies": {"@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/theme-one-dark": "^6.1.2", "@element-plus/icons-vue": "^2.3.1", "@form-create/designer": "^3.2.2", "@form-create/element-ui": "^3.2.0", "@form-create/utils": "^3.2.0", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-regular-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/vue-fontawesome": "^3.0.6", "@vueuse/core": "^10.9.0", "aieditor": "^1.0.13", "animate.css": "^4.1.1", "axios": "^1.6.8", "codemirror": "^6.0.1", "echarts": "^5.5.1", "element-plus": "^2.7.7", "highlight.js": "^11.9.0", "js-pinyin": "^0.2.5", "jszip": "^3.10.1", "loadjs": "^4.2.0", "pinia": "^2.1.7", "pinia-plugin-persist": "^1.0.0", "spark-md5": "^3.0.2", "uuid": "^11.1.0", "vue": "^3.4.29", "vue-codemirror": "^6.1.1", "vue-dompurify-html": "^5.1.0", "vue-i18n": "9", "vue-router": "^4.4.0"}, "devDependencies": {"@fortawesome/free-brands-svg-icons": "^6.5.1", "@vitejs/plugin-vue": "^5.0.5", "less": "^4.3.0", "nprogress": "^0.2.0", "sass": "^1.77.8", "vite": "^5.3.1", "vite-plugin-css-injected-by-js": "^3.5.1", "vite-plugin-externals": "^0.6.2", "vite-plugin-html": "^3.2.2", "vite-plugin-progress": "^0.0.7", "vite-plugin-static-copy": "^1.0.6"}}